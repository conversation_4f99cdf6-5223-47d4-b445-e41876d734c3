"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-from-parse5";
exports.ids = ["vendor-chunks/hast-util-from-parse5"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-from-parse5/lib/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/hast-util-from-parse5/lib/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromParse5: () => (/* binding */ fromParse5)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var hastscript__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! hastscript */ \"(ssr)/./node_modules/hastscript/lib/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var vfile_location__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! vfile-location */ \"(ssr)/./node_modules/vfile-location/lib/index.js\");\n/* harmony import */ var web_namespaces__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! web-namespaces */ \"(ssr)/./node_modules/web-namespaces/index.js\");\n/**\n * @import {ElementData, Element, Nodes, RootContent, Root} from 'hast'\n * @import {DefaultTreeAdapterMap, Token} from 'parse5'\n * @import {Schema} from 'property-information'\n * @import {Point, Position} from 'unist'\n * @import {VFile} from 'vfile'\n * @import {Options} from 'hast-util-from-parse5'\n */\n\n/**\n * @typedef State\n *   Info passed around about the current state.\n * @property {VFile | undefined} file\n *   Corresponding file.\n * @property {boolean} location\n *   Whether location info was found.\n * @property {Schema} schema\n *   Current schema.\n * @property {boolean | undefined} verbose\n *   Add extra positional info.\n */\n\n\n\n\n\n\n\nconst own = {}.hasOwnProperty\n/** @type {unknown} */\n// type-coverage:ignore-next-line\nconst proto = Object.prototype\n\n/**\n * Transform a `parse5` AST to hast.\n *\n * @param {DefaultTreeAdapterMap['node']} tree\n *   `parse5` tree to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {Nodes}\n *   hast tree.\n */\nfunction fromParse5(tree, options) {\n  const settings = options || {}\n\n  return one(\n    {\n      file: settings.file || undefined,\n      location: false,\n      schema: settings.space === 'svg' ? property_information__WEBPACK_IMPORTED_MODULE_0__.svg : property_information__WEBPACK_IMPORTED_MODULE_0__.html,\n      verbose: settings.verbose || false\n    },\n    tree\n  )\n}\n\n/**\n * Transform a node.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {DefaultTreeAdapterMap['node']} node\n *   p5 node.\n * @returns {Nodes}\n *   hast node.\n */\nfunction one(state, node) {\n  /** @type {Nodes} */\n  let result\n\n  switch (node.nodeName) {\n    case '#comment': {\n      const reference = /** @type {DefaultTreeAdapterMap['commentNode']} */ (\n        node\n      )\n      result = {type: 'comment', value: reference.data}\n      patch(state, reference, result)\n      return result\n    }\n\n    case '#document':\n    case '#document-fragment': {\n      const reference =\n        /** @type {DefaultTreeAdapterMap['document'] | DefaultTreeAdapterMap['documentFragment']} */ (\n          node\n        )\n      const quirksMode =\n        'mode' in reference\n          ? reference.mode === 'quirks' || reference.mode === 'limited-quirks'\n          : false\n\n      result = {\n        type: 'root',\n        children: all(state, node.childNodes),\n        data: {quirksMode}\n      }\n\n      if (state.file && state.location) {\n        const document = String(state.file)\n        const loc = (0,vfile_location__WEBPACK_IMPORTED_MODULE_1__.location)(document)\n        const start = loc.toPoint(0)\n        const end = loc.toPoint(document.length)\n        // Always defined as we give valid input.\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(start, 'expected `start`')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(end, 'expected `end`')\n        result.position = {start, end}\n      }\n\n      return result\n    }\n\n    case '#documentType': {\n      const reference = /** @type {DefaultTreeAdapterMap['documentType']} */ (\n        node\n      )\n      result = {type: 'doctype'}\n      patch(state, reference, result)\n      return result\n    }\n\n    case '#text': {\n      const reference = /** @type {DefaultTreeAdapterMap['textNode']} */ (node)\n      result = {type: 'text', value: reference.value}\n      patch(state, reference, result)\n      return result\n    }\n\n    // Element.\n    default: {\n      const reference = /** @type {DefaultTreeAdapterMap['element']} */ (node)\n      result = element(state, reference)\n      return result\n    }\n  }\n}\n\n/**\n * Transform children.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Array<DefaultTreeAdapterMap['node']>} nodes\n *   Nodes.\n * @returns {Array<RootContent>}\n *   hast nodes.\n */\nfunction all(state, nodes) {\n  let index = -1\n  /** @type {Array<RootContent>} */\n  const results = []\n\n  while (++index < nodes.length) {\n    // Assume no roots in `nodes`.\n    const result = /** @type {RootContent} */ (one(state, nodes[index]))\n    results.push(result)\n  }\n\n  return results\n}\n\n/**\n * Transform an element.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {DefaultTreeAdapterMap['element']} node\n *   `parse5` node to transform.\n * @returns {Element}\n *   hast node.\n */\nfunction element(state, node) {\n  const schema = state.schema\n\n  state.schema = node.namespaceURI === web_namespaces__WEBPACK_IMPORTED_MODULE_3__.webNamespaces.svg ? property_information__WEBPACK_IMPORTED_MODULE_0__.svg : property_information__WEBPACK_IMPORTED_MODULE_0__.html\n\n  // Props.\n  let index = -1\n  /** @type {Record<string, string>} */\n  const properties = {}\n\n  while (++index < node.attrs.length) {\n    const attribute = node.attrs[index]\n    const name =\n      (attribute.prefix ? attribute.prefix + ':' : '') + attribute.name\n    if (!own.call(proto, name)) {\n      properties[name] = attribute.value\n    }\n  }\n\n  // Build.\n  const x = state.schema.space === 'svg' ? hastscript__WEBPACK_IMPORTED_MODULE_4__.s : hastscript__WEBPACK_IMPORTED_MODULE_4__.h\n  const result = x(node.tagName, properties, all(state, node.childNodes))\n  patch(state, node, result)\n\n  // Switch content.\n  if (result.tagName === 'template') {\n    const reference = /** @type {DefaultTreeAdapterMap['template']} */ (node)\n    const pos = reference.sourceCodeLocation\n    const startTag = pos && pos.startTag && position(pos.startTag)\n    const endTag = pos && pos.endTag && position(pos.endTag)\n\n    // Root in, root out.\n    const content = /** @type {Root} */ (one(state, reference.content))\n\n    if (startTag && endTag && state.file) {\n      content.position = {start: startTag.end, end: endTag.start}\n    }\n\n    result.content = content\n  }\n\n  state.schema = schema\n\n  return result\n}\n\n/**\n * Patch positional info from `from` onto `to`.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {DefaultTreeAdapterMap['node']} from\n *   p5 node.\n * @param {Nodes} to\n *   hast node.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(state, from, to) {\n  if ('sourceCodeLocation' in from && from.sourceCodeLocation && state.file) {\n    const position = createLocation(state, to, from.sourceCodeLocation)\n\n    if (position) {\n      state.location = true\n      to.position = position\n    }\n  }\n}\n\n/**\n * Create clean positional information.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Nodes} node\n *   hast node.\n * @param {Token.ElementLocation} location\n *   p5 location info.\n * @returns {Position | undefined}\n *   Position, or nothing.\n */\nfunction createLocation(state, node, location) {\n  const result = position(location)\n\n  if (node.type === 'element') {\n    const tail = node.children[node.children.length - 1]\n\n    // Bug for unclosed with children.\n    // See: <https://github.com/inikulin/parse5/issues/109>.\n    if (\n      result &&\n      !location.endTag &&\n      tail &&\n      tail.position &&\n      tail.position.end\n    ) {\n      result.end = Object.assign({}, tail.position.end)\n    }\n\n    if (state.verbose) {\n      /** @type {Record<string, Position | undefined>} */\n      const properties = {}\n      /** @type {string} */\n      let key\n\n      if (location.attrs) {\n        for (key in location.attrs) {\n          if (own.call(location.attrs, key)) {\n            properties[(0,property_information__WEBPACK_IMPORTED_MODULE_5__.find)(state.schema, key).property] = position(\n              location.attrs[key]\n            )\n          }\n        }\n      }\n\n      (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(location.startTag, 'a start tag should exist')\n      const opening = position(location.startTag)\n      const closing = location.endTag ? position(location.endTag) : undefined\n      /** @type {ElementData['position']} */\n      const data = {opening}\n      if (closing) data.closing = closing\n      data.properties = properties\n\n      node.data = {position: data}\n    }\n  }\n\n  return result\n}\n\n/**\n * Turn a p5 location into a position.\n *\n * @param {Token.Location} loc\n *   Location.\n * @returns {Position | undefined}\n *   Position or nothing.\n */\nfunction position(loc) {\n  const start = point({\n    line: loc.startLine,\n    column: loc.startCol,\n    offset: loc.startOffset\n  })\n  const end = point({\n    line: loc.endLine,\n    column: loc.endCol,\n    offset: loc.endOffset\n  })\n\n  // @ts-expect-error: we do use `undefined` for points if one or the other\n  // exists.\n  return start || end ? {start, end} : undefined\n}\n\n/**\n * Filter out invalid points.\n *\n * @param {Point} point\n *   Point with potentially `undefined` values.\n * @returns {Point | undefined}\n *   Point or nothing.\n */\nfunction point(point) {\n  return point.line && point.column ? point : undefined\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-from-parse5/lib/index.js\n");

/***/ })

};
;