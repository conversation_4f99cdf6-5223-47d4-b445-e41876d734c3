/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/set-student-claims/route";
exports.ids = ["app/api/auth/set-student-claims/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fset-student-claims%2Froute&page=%2Fapi%2Fauth%2Fset-student-claims%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fset-student-claims%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fset-student-claims%2Froute&page=%2Fapi%2Fauth%2Fset-student-claims%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fset-student-claims%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_auth_set_student_claims_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/set-student-claims/route.ts */ \"(rsc)/./src/app/api/auth/set-student-claims/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/set-student-claims/route\",\n        pathname: \"/api/auth/set-student-claims\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/set-student-claims/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\api\\\\auth\\\\set-student-claims\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_auth_set_student_claims_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fset-student-claims%2Froute&page=%2Fapi%2Fauth%2Fset-student-claims%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fset-student-claims%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/set-student-claims/route.ts":
/*!******************************************************!*\
  !*** ./src/app/api/auth/set-student-claims/route.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var firebase_admin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase-admin */ \"firebase-admin\");\n/* harmony import */ var firebase_admin__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(firebase_admin__WEBPACK_IMPORTED_MODULE_1__);\n// src/app/api/auth/set-student-claims/route.ts\n\n\nasync function POST(request) {\n    try {\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader || !authHeader.startsWith('Bearer ')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Missing or invalid authorization header'\n            }, {\n                status: 401\n            });\n        }\n        const idToken = authHeader.replace('Bearer ', '');\n        const { studentId } = await request.json();\n        if (!studentId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Student ID is required'\n            }, {\n                status: 400\n            });\n        }\n        // Initialize Firebase Admin directly with the same credentials as student-login\n        let app;\n        try {\n            // Validate environment variables first\n            const requiredVars = [\n                'FIREBASE_PROJECT_ID',\n                'FIREBASE_CLIENT_EMAIL',\n                'FIREBASE_PRIVATE_KEY_ID',\n                'FIREBASE_CLIENT_ID',\n                'FIREBASE_PRIVATE_KEY'\n            ];\n            const missingVars = requiredVars.filter((varName)=>!process.env[varName]);\n            if (missingVars.length > 0) {\n                console.error('[Set Student Claims] Missing environment variables:', missingVars);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'Server configuration error'\n                }, {\n                    status: 500\n                });\n            }\n            // Process the private key to handle different newline formats\n            let privateKey = process.env.FIREBASE_PRIVATE_KEY;\n            if (privateKey) {\n                // Remove surrounding quotes if they exist\n                if (privateKey.startsWith('\"') && privateKey.endsWith('\"')) {\n                    privateKey = privateKey.slice(1, -1);\n                }\n                // Handle different possible newline encodings\n                privateKey = privateKey.replace(/\\\\\\\\n/g, '\\n') // Handle double-escaped newlines\n                .replace(/\\\\n/g, '\\n'); // Handle escaped newlines\n                console.log('[Set Student Claims] Processed private key length:', privateKey.length);\n                console.log('[Set Student Claims] Private key starts with:', privateKey.substring(0, 30));\n            }\n            const serviceAccount = {\n                \"type\": \"service_account\",\n                \"project_id\": process.env.FIREBASE_PROJECT_ID,\n                \"private_key_id\": process.env.FIREBASE_PRIVATE_KEY_ID,\n                \"private_key\": privateKey,\n                \"client_email\": process.env.FIREBASE_CLIENT_EMAIL,\n                \"client_id\": process.env.FIREBASE_CLIENT_ID,\n                \"auth_uri\": \"https://accounts.google.com/o/oauth2/auth\",\n                \"token_uri\": \"https://oauth2.googleapis.com/token\",\n                \"auth_provider_x509_cert_url\": \"https://www.googleapis.com/oauth2/v1/certs\",\n                \"client_x509_cert_url\": `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.FIREBASE_CLIENT_EMAIL?.replace('@', '%40')}`,\n                \"universe_domain\": \"googleapis.com\"\n            };\n            console.log('[Set Student Claims] Initializing Firebase Admin...');\n            console.log('[Set Student Claims] Project ID:', serviceAccount.project_id);\n            console.log('[Set Student Claims] Client Email:', serviceAccount.client_email);\n            // Create a unique app name for this route\n            const appName = `set-claims-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;\n            // Clean up any existing apps with similar names to avoid conflicts\n            const existingApps = (firebase_admin__WEBPACK_IMPORTED_MODULE_1___default().apps);\n            existingApps.forEach((existingApp)=>{\n                if (existingApp && existingApp.name && existingApp.name.startsWith('set-claims-')) {\n                    try {\n                        firebase_admin__WEBPACK_IMPORTED_MODULE_1___default().deleteApp(existingApp);\n                        console.log(`[Set Student Claims] Cleaned up existing app: ${existingApp.name}`);\n                    } catch (cleanupError) {\n                        console.warn(`[Set Student Claims] Could not clean up app ${existingApp.name}:`, cleanupError);\n                    }\n                }\n            });\n            app = firebase_admin__WEBPACK_IMPORTED_MODULE_1___default().initializeApp({\n                credential: firebase_admin__WEBPACK_IMPORTED_MODULE_1___default().credential.cert(serviceAccount),\n                projectId: serviceAccount.project_id\n            }, appName);\n            console.log('[Set Student Claims] Firebase Admin initialized successfully with app name:', appName);\n        } catch (initError) {\n            console.error('[Set Student Claims] Firebase initialization error:', initError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Authentication service unavailable'\n            }, {\n                status: 500\n            });\n        }\n        const firebaseAdminAuth = app.auth();\n        // Verify the ID token first\n        let decodedToken;\n        try {\n            decodedToken = await firebaseAdminAuth.verifyIdToken(idToken);\n        } catch (error) {\n            console.error('[set-student-claims] Token verification failed:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Invalid ID token'\n            }, {\n                status: 401\n            });\n        }\n        const uid = decodedToken.uid;\n        // Set custom claims for the user\n        const customClaims = {\n            role: 'student',\n            student_id: studentId\n        };\n        try {\n            await firebaseAdminAuth.setCustomUserClaims(uid, customClaims);\n            console.log(`[set-student-claims] Successfully set claims for user ${uid}, student ID: ${studentId}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'Student claims set successfully',\n                claims: customClaims\n            });\n        } catch (error) {\n            console.error('[set-student-claims] Error setting custom claims:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Failed to set custom claims'\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error('[set-student-claims] Unexpected error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hdXRoL3NldC1zdHVkZW50LWNsYWltcy9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUEsK0NBQStDO0FBRVM7QUFDckI7QUFFNUIsZUFBZUUsS0FBS0MsT0FBb0I7SUFDN0MsSUFBSTtRQUNGLE1BQU1DLGFBQWFELFFBQVFFLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDO1FBQ3ZDLElBQUksQ0FBQ0YsY0FBYyxDQUFDQSxXQUFXRyxVQUFVLENBQUMsWUFBWTtZQUNwRCxPQUFPUCxxREFBWUEsQ0FBQ1EsSUFBSSxDQUN0QjtnQkFBRUMsU0FBUztnQkFBT0MsT0FBTztZQUEwQyxHQUNuRTtnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsTUFBTUMsVUFBVVIsV0FBV1MsT0FBTyxDQUFDLFdBQVc7UUFDOUMsTUFBTSxFQUFFQyxTQUFTLEVBQUUsR0FBRyxNQUFNWCxRQUFRSyxJQUFJO1FBRXhDLElBQUksQ0FBQ00sV0FBVztZQUNkLE9BQU9kLHFEQUFZQSxDQUFDUSxJQUFJLENBQ3RCO2dCQUFFQyxTQUFTO2dCQUFPQyxPQUFPO1lBQXlCLEdBQ2xEO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxnRkFBZ0Y7UUFDaEYsSUFBSUk7UUFFSixJQUFJO1lBQ0YsdUNBQXVDO1lBQ3ZDLE1BQU1DLGVBQWU7Z0JBQ25CO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2FBQ0Q7WUFFRCxNQUFNQyxjQUFjRCxhQUFhRSxNQUFNLENBQUNDLENBQUFBLFVBQVcsQ0FBQ0MsUUFBUUMsR0FBRyxDQUFDRixRQUFRO1lBRXhFLElBQUlGLFlBQVlLLE1BQU0sR0FBRyxHQUFHO2dCQUMxQkMsUUFBUWIsS0FBSyxDQUFDLHVEQUF1RE87Z0JBQ3JFLE9BQU9qQixxREFBWUEsQ0FBQ1EsSUFBSSxDQUN0QjtvQkFBRUMsU0FBUztvQkFBT0MsT0FBTztnQkFBNkIsR0FDdEQ7b0JBQUVDLFFBQVE7Z0JBQUk7WUFFbEI7WUFFQSw4REFBOEQ7WUFDOUQsSUFBSWEsYUFBYUosUUFBUUMsR0FBRyxDQUFDSSxvQkFBb0I7WUFDakQsSUFBSUQsWUFBWTtnQkFDZCwwQ0FBMEM7Z0JBQzFDLElBQUlBLFdBQVdqQixVQUFVLENBQUMsUUFBUWlCLFdBQVdFLFFBQVEsQ0FBQyxNQUFNO29CQUMxREYsYUFBYUEsV0FBV0csS0FBSyxDQUFDLEdBQUcsQ0FBQztnQkFDcEM7Z0JBRUEsOENBQThDO2dCQUM5Q0gsYUFBYUEsV0FDVlgsT0FBTyxDQUFDLFVBQVUsTUFBTyxpQ0FBaUM7aUJBQzFEQSxPQUFPLENBQUMsUUFBUSxPQUFTLDBCQUEwQjtnQkFFdERVLFFBQVFLLEdBQUcsQ0FBQyxzREFBc0RKLFdBQVdGLE1BQU07Z0JBQ25GQyxRQUFRSyxHQUFHLENBQUMsaURBQWlESixXQUFXSyxTQUFTLENBQUMsR0FBRztZQUN2RjtZQUVBLE1BQU1DLGlCQUFpQjtnQkFDckIsUUFBUTtnQkFDUixjQUFjVixRQUFRQyxHQUFHLENBQUNVLG1CQUFtQjtnQkFDN0Msa0JBQWtCWCxRQUFRQyxHQUFHLENBQUNXLHVCQUF1QjtnQkFDckQsZUFBZVI7Z0JBQ2YsZ0JBQWdCSixRQUFRQyxHQUFHLENBQUNZLHFCQUFxQjtnQkFDakQsYUFBYWIsUUFBUUMsR0FBRyxDQUFDYSxrQkFBa0I7Z0JBQzNDLFlBQVk7Z0JBQ1osYUFBYTtnQkFDYiwrQkFBK0I7Z0JBQy9CLHdCQUF3QixDQUFDLGtEQUFrRCxFQUFFZCxRQUFRQyxHQUFHLENBQUNZLHFCQUFxQixFQUFFcEIsUUFBUSxLQUFLLFFBQVE7Z0JBQ3JJLG1CQUFtQjtZQUNyQjtZQUVBVSxRQUFRSyxHQUFHLENBQUM7WUFDWkwsUUFBUUssR0FBRyxDQUFDLG9DQUFvQ0UsZUFBZUssVUFBVTtZQUN6RVosUUFBUUssR0FBRyxDQUFDLHNDQUFzQ0UsZUFBZU0sWUFBWTtZQUU3RSwwQ0FBMEM7WUFDMUMsTUFBTUMsVUFBVSxDQUFDLFdBQVcsRUFBRUMsS0FBS0MsR0FBRyxHQUFHLENBQUMsRUFBRUMsS0FBS0MsTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSWIsU0FBUyxDQUFDLEdBQUcsSUFBSTtZQUV4RixtRUFBbUU7WUFDbkUsTUFBTWMsZUFBZTFDLDREQUFVO1lBQy9CMEMsYUFBYUUsT0FBTyxDQUFDQyxDQUFBQTtnQkFDbkIsSUFBSUEsZUFBZUEsWUFBWUMsSUFBSSxJQUFJRCxZQUFZQyxJQUFJLENBQUN4QyxVQUFVLENBQUMsZ0JBQWdCO29CQUNqRixJQUFJO3dCQUNGTiwrREFBZSxDQUFDNkM7d0JBQ2hCdkIsUUFBUUssR0FBRyxDQUFDLENBQUMsOENBQThDLEVBQUVrQixZQUFZQyxJQUFJLEVBQUU7b0JBQ2pGLEVBQUUsT0FBT0UsY0FBYzt3QkFDckIxQixRQUFRMkIsSUFBSSxDQUFDLENBQUMsNENBQTRDLEVBQUVKLFlBQVlDLElBQUksQ0FBQyxDQUFDLENBQUMsRUFBRUU7b0JBQ25GO2dCQUNGO1lBQ0Y7WUFFQWxDLE1BQU1kLG1FQUFtQixDQUFDO2dCQUN4Qm1ELFlBQVluRCxnRUFBZ0IsQ0FBQ29ELElBQUksQ0FBQ3ZCO2dCQUNsQ3dCLFdBQVd4QixlQUFlSyxVQUFVO1lBQ3RDLEdBQUdFO1lBRUhkLFFBQVFLLEdBQUcsQ0FBQywrRUFBK0VTO1FBRTdGLEVBQUUsT0FBT2tCLFdBQWdCO1lBQ3ZCaEMsUUFBUWIsS0FBSyxDQUFDLHVEQUF1RDZDO1lBQ3JFLE9BQU92RCxxREFBWUEsQ0FBQ1EsSUFBSSxDQUN0QjtnQkFBRUMsU0FBUztnQkFBT0MsT0FBTztZQUFxQyxHQUM5RDtnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsTUFBTTZDLG9CQUFvQnpDLElBQUkwQyxJQUFJO1FBRWxDLDRCQUE0QjtRQUM1QixJQUFJQztRQUNKLElBQUk7WUFDRkEsZUFBZSxNQUFNRixrQkFBa0JHLGFBQWEsQ0FBQy9DO1FBQ3ZELEVBQUUsT0FBT0YsT0FBTztZQUNkYSxRQUFRYixLQUFLLENBQUMsbURBQW1EQTtZQUNqRSxPQUFPVixxREFBWUEsQ0FBQ1EsSUFBSSxDQUN0QjtnQkFBRUMsU0FBUztnQkFBT0MsT0FBTztZQUFtQixHQUM1QztnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsTUFBTWlELE1BQU1GLGFBQWFFLEdBQUc7UUFFNUIsaUNBQWlDO1FBQ2pDLE1BQU1DLGVBQWU7WUFDbkJDLE1BQU07WUFDTkMsWUFBWWpEO1FBQ2Q7UUFFQSxJQUFJO1lBQ0YsTUFBTTBDLGtCQUFrQlEsbUJBQW1CLENBQUNKLEtBQUtDO1lBQ2pEdEMsUUFBUUssR0FBRyxDQUFDLENBQUMsc0RBQXNELEVBQUVnQyxJQUFJLGNBQWMsRUFBRTlDLFdBQVc7WUFFcEcsT0FBT2QscURBQVlBLENBQUNRLElBQUksQ0FBQztnQkFDdkJDLFNBQVM7Z0JBQ1R3RCxTQUFTO2dCQUNUQyxRQUFRTDtZQUNWO1FBQ0YsRUFBRSxPQUFPbkQsT0FBTztZQUNkYSxRQUFRYixLQUFLLENBQUMscURBQXFEQTtZQUNuRSxPQUFPVixxREFBWUEsQ0FBQ1EsSUFBSSxDQUN0QjtnQkFBRUMsU0FBUztnQkFBT0MsT0FBTztZQUE4QixHQUN2RDtnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO0lBRUYsRUFBRSxPQUFPRCxPQUFPO1FBQ2RhLFFBQVFiLEtBQUssQ0FBQywwQ0FBMENBO1FBQ3hELE9BQU9WLHFEQUFZQSxDQUFDUSxJQUFJLENBQ3RCO1lBQUVDLFNBQVM7WUFBT0MsT0FBTztRQUF3QixHQUNqRDtZQUFFQyxRQUFRO1FBQUk7SUFFbEI7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwY1xcT25lRHJpdmVcXERlc2t0b3BcXERlc2t0b3BcXFNvbHludGFfV2Vic2l0ZVxcZnJvbnRlbmRcXGxlc3Nvbi1wbGF0Zm9ybVxcc3JjXFxhcHBcXGFwaVxcYXV0aFxcc2V0LXN0dWRlbnQtY2xhaW1zXFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvYXBwL2FwaS9hdXRoL3NldC1zdHVkZW50LWNsYWltcy9yb3V0ZS50c1xyXG5cclxuaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJztcclxuaW1wb3J0IGFkbWluIGZyb20gJ2ZpcmViYXNlLWFkbWluJztcclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQT1NUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IGF1dGhIZWFkZXIgPSByZXF1ZXN0LmhlYWRlcnMuZ2V0KCdhdXRob3JpemF0aW9uJyk7XHJcbiAgICBpZiAoIWF1dGhIZWFkZXIgfHwgIWF1dGhIZWFkZXIuc3RhcnRzV2l0aCgnQmVhcmVyICcpKSB7XHJcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcclxuICAgICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ01pc3Npbmcgb3IgaW52YWxpZCBhdXRob3JpemF0aW9uIGhlYWRlcicgfSxcclxuICAgICAgICB7IHN0YXR1czogNDAxIH1cclxuICAgICAgKTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBpZFRva2VuID0gYXV0aEhlYWRlci5yZXBsYWNlKCdCZWFyZXIgJywgJycpO1xyXG4gICAgY29uc3QgeyBzdHVkZW50SWQgfSA9IGF3YWl0IHJlcXVlc3QuanNvbigpO1xyXG5cclxuICAgIGlmICghc3R1ZGVudElkKSB7XHJcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcclxuICAgICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ1N0dWRlbnQgSUQgaXMgcmVxdWlyZWQnIH0sXHJcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XHJcbiAgICAgICk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gSW5pdGlhbGl6ZSBGaXJlYmFzZSBBZG1pbiBkaXJlY3RseSB3aXRoIHRoZSBzYW1lIGNyZWRlbnRpYWxzIGFzIHN0dWRlbnQtbG9naW5cclxuICAgIGxldCBhcHA6IGFkbWluLmFwcC5BcHA7XHJcbiAgICBcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIFZhbGlkYXRlIGVudmlyb25tZW50IHZhcmlhYmxlcyBmaXJzdFxyXG4gICAgICBjb25zdCByZXF1aXJlZFZhcnMgPSBbXHJcbiAgICAgICAgJ0ZJUkVCQVNFX1BST0pFQ1RfSUQnLFxyXG4gICAgICAgICdGSVJFQkFTRV9DTElFTlRfRU1BSUwnLCBcclxuICAgICAgICAnRklSRUJBU0VfUFJJVkFURV9LRVlfSUQnLFxyXG4gICAgICAgICdGSVJFQkFTRV9DTElFTlRfSUQnLFxyXG4gICAgICAgICdGSVJFQkFTRV9QUklWQVRFX0tFWSdcclxuICAgICAgXTtcclxuICAgICAgXHJcbiAgICAgIGNvbnN0IG1pc3NpbmdWYXJzID0gcmVxdWlyZWRWYXJzLmZpbHRlcih2YXJOYW1lID0+ICFwcm9jZXNzLmVudlt2YXJOYW1lXSk7XHJcbiAgICAgIFxyXG4gICAgICBpZiAobWlzc2luZ1ZhcnMubGVuZ3RoID4gMCkge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1tTZXQgU3R1ZGVudCBDbGFpbXNdIE1pc3NpbmcgZW52aXJvbm1lbnQgdmFyaWFibGVzOicsIG1pc3NpbmdWYXJzKTtcclxuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXHJcbiAgICAgICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ1NlcnZlciBjb25maWd1cmF0aW9uIGVycm9yJyB9LFxyXG4gICAgICAgICAgeyBzdGF0dXM6IDUwMCB9XHJcbiAgICAgICAgKTtcclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgLy8gUHJvY2VzcyB0aGUgcHJpdmF0ZSBrZXkgdG8gaGFuZGxlIGRpZmZlcmVudCBuZXdsaW5lIGZvcm1hdHNcclxuICAgICAgbGV0IHByaXZhdGVLZXkgPSBwcm9jZXNzLmVudi5GSVJFQkFTRV9QUklWQVRFX0tFWTtcclxuICAgICAgaWYgKHByaXZhdGVLZXkpIHtcclxuICAgICAgICAvLyBSZW1vdmUgc3Vycm91bmRpbmcgcXVvdGVzIGlmIHRoZXkgZXhpc3RcclxuICAgICAgICBpZiAocHJpdmF0ZUtleS5zdGFydHNXaXRoKCdcIicpICYmIHByaXZhdGVLZXkuZW5kc1dpdGgoJ1wiJykpIHtcclxuICAgICAgICAgIHByaXZhdGVLZXkgPSBwcml2YXRlS2V5LnNsaWNlKDEsIC0xKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgXHJcbiAgICAgICAgLy8gSGFuZGxlIGRpZmZlcmVudCBwb3NzaWJsZSBuZXdsaW5lIGVuY29kaW5nc1xyXG4gICAgICAgIHByaXZhdGVLZXkgPSBwcml2YXRlS2V5XHJcbiAgICAgICAgICAucmVwbGFjZSgvXFxcXFxcXFxuL2csICdcXG4nKSAgLy8gSGFuZGxlIGRvdWJsZS1lc2NhcGVkIG5ld2xpbmVzXHJcbiAgICAgICAgICAucmVwbGFjZSgvXFxcXG4vZywgJ1xcbicpOyAgIC8vIEhhbmRsZSBlc2NhcGVkIG5ld2xpbmVzXHJcbiAgICAgICAgXHJcbiAgICAgICAgY29uc29sZS5sb2coJ1tTZXQgU3R1ZGVudCBDbGFpbXNdIFByb2Nlc3NlZCBwcml2YXRlIGtleSBsZW5ndGg6JywgcHJpdmF0ZUtleS5sZW5ndGgpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdbU2V0IFN0dWRlbnQgQ2xhaW1zXSBQcml2YXRlIGtleSBzdGFydHMgd2l0aDonLCBwcml2YXRlS2V5LnN1YnN0cmluZygwLCAzMCkpO1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICBjb25zdCBzZXJ2aWNlQWNjb3VudCA9IHtcclxuICAgICAgICBcInR5cGVcIjogXCJzZXJ2aWNlX2FjY291bnRcIixcclxuICAgICAgICBcInByb2plY3RfaWRcIjogcHJvY2Vzcy5lbnYuRklSRUJBU0VfUFJPSkVDVF9JRCxcclxuICAgICAgICBcInByaXZhdGVfa2V5X2lkXCI6IHByb2Nlc3MuZW52LkZJUkVCQVNFX1BSSVZBVEVfS0VZX0lELFxyXG4gICAgICAgIFwicHJpdmF0ZV9rZXlcIjogcHJpdmF0ZUtleSxcclxuICAgICAgICBcImNsaWVudF9lbWFpbFwiOiBwcm9jZXNzLmVudi5GSVJFQkFTRV9DTElFTlRfRU1BSUwsXHJcbiAgICAgICAgXCJjbGllbnRfaWRcIjogcHJvY2Vzcy5lbnYuRklSRUJBU0VfQ0xJRU5UX0lELFxyXG4gICAgICAgIFwiYXV0aF91cmlcIjogXCJodHRwczovL2FjY291bnRzLmdvb2dsZS5jb20vby9vYXV0aDIvYXV0aFwiLFxyXG4gICAgICAgIFwidG9rZW5fdXJpXCI6IFwiaHR0cHM6Ly9vYXV0aDIuZ29vZ2xlYXBpcy5jb20vdG9rZW5cIixcclxuICAgICAgICBcImF1dGhfcHJvdmlkZXJfeDUwOV9jZXJ0X3VybFwiOiBcImh0dHBzOi8vd3d3Lmdvb2dsZWFwaXMuY29tL29hdXRoMi92MS9jZXJ0c1wiLFxyXG4gICAgICAgIFwiY2xpZW50X3g1MDlfY2VydF91cmxcIjogYGh0dHBzOi8vd3d3Lmdvb2dsZWFwaXMuY29tL3JvYm90L3YxL21ldGFkYXRhL3g1MDkvJHtwcm9jZXNzLmVudi5GSVJFQkFTRV9DTElFTlRfRU1BSUw/LnJlcGxhY2UoJ0AnLCAnJTQwJyl9YCxcclxuICAgICAgICBcInVuaXZlcnNlX2RvbWFpblwiOiBcImdvb2dsZWFwaXMuY29tXCJcclxuICAgICAgfTtcclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKCdbU2V0IFN0dWRlbnQgQ2xhaW1zXSBJbml0aWFsaXppbmcgRmlyZWJhc2UgQWRtaW4uLi4nKTtcclxuICAgICAgY29uc29sZS5sb2coJ1tTZXQgU3R1ZGVudCBDbGFpbXNdIFByb2plY3QgSUQ6Jywgc2VydmljZUFjY291bnQucHJvamVjdF9pZCk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdbU2V0IFN0dWRlbnQgQ2xhaW1zXSBDbGllbnQgRW1haWw6Jywgc2VydmljZUFjY291bnQuY2xpZW50X2VtYWlsKTtcclxuICAgICAgXHJcbiAgICAgIC8vIENyZWF0ZSBhIHVuaXF1ZSBhcHAgbmFtZSBmb3IgdGhpcyByb3V0ZVxyXG4gICAgICBjb25zdCBhcHBOYW1lID0gYHNldC1jbGFpbXMtJHtEYXRlLm5vdygpfS0ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cmluZygyLCA4KX1gO1xyXG4gICAgICBcclxuICAgICAgLy8gQ2xlYW4gdXAgYW55IGV4aXN0aW5nIGFwcHMgd2l0aCBzaW1pbGFyIG5hbWVzIHRvIGF2b2lkIGNvbmZsaWN0c1xyXG4gICAgICBjb25zdCBleGlzdGluZ0FwcHMgPSBhZG1pbi5hcHBzO1xyXG4gICAgICBleGlzdGluZ0FwcHMuZm9yRWFjaChleGlzdGluZ0FwcCA9PiB7XHJcbiAgICAgICAgaWYgKGV4aXN0aW5nQXBwICYmIGV4aXN0aW5nQXBwLm5hbWUgJiYgZXhpc3RpbmdBcHAubmFtZS5zdGFydHNXaXRoKCdzZXQtY2xhaW1zLScpKSB7XHJcbiAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBhZG1pbi5kZWxldGVBcHAoZXhpc3RpbmdBcHApO1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhgW1NldCBTdHVkZW50IENsYWltc10gQ2xlYW5lZCB1cCBleGlzdGluZyBhcHA6ICR7ZXhpc3RpbmdBcHAubmFtZX1gKTtcclxuICAgICAgICAgIH0gY2F0Y2ggKGNsZWFudXBFcnJvcikge1xyXG4gICAgICAgICAgICBjb25zb2xlLndhcm4oYFtTZXQgU3R1ZGVudCBDbGFpbXNdIENvdWxkIG5vdCBjbGVhbiB1cCBhcHAgJHtleGlzdGluZ0FwcC5uYW1lfTpgLCBjbGVhbnVwRXJyb3IpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfSk7XHJcbiAgICAgIFxyXG4gICAgICBhcHAgPSBhZG1pbi5pbml0aWFsaXplQXBwKHtcclxuICAgICAgICBjcmVkZW50aWFsOiBhZG1pbi5jcmVkZW50aWFsLmNlcnQoc2VydmljZUFjY291bnQpLFxyXG4gICAgICAgIHByb2plY3RJZDogc2VydmljZUFjY291bnQucHJvamVjdF9pZFxyXG4gICAgICB9LCBhcHBOYW1lKTtcclxuICAgICAgXHJcbiAgICAgIGNvbnNvbGUubG9nKCdbU2V0IFN0dWRlbnQgQ2xhaW1zXSBGaXJlYmFzZSBBZG1pbiBpbml0aWFsaXplZCBzdWNjZXNzZnVsbHkgd2l0aCBhcHAgbmFtZTonLCBhcHBOYW1lKTtcclxuICAgICAgXHJcbiAgICB9IGNhdGNoIChpbml0RXJyb3I6IGFueSkge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdbU2V0IFN0dWRlbnQgQ2xhaW1zXSBGaXJlYmFzZSBpbml0aWFsaXphdGlvbiBlcnJvcjonLCBpbml0RXJyb3IpO1xyXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXHJcbiAgICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdBdXRoZW50aWNhdGlvbiBzZXJ2aWNlIHVuYXZhaWxhYmxlJyB9LFxyXG4gICAgICAgIHsgc3RhdHVzOiA1MDAgfVxyXG4gICAgICApO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGZpcmViYXNlQWRtaW5BdXRoID0gYXBwLmF1dGgoKTtcclxuXHJcbiAgICAvLyBWZXJpZnkgdGhlIElEIHRva2VuIGZpcnN0XHJcbiAgICBsZXQgZGVjb2RlZFRva2VuO1xyXG4gICAgdHJ5IHtcclxuICAgICAgZGVjb2RlZFRva2VuID0gYXdhaXQgZmlyZWJhc2VBZG1pbkF1dGgudmVyaWZ5SWRUb2tlbihpZFRva2VuKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1tzZXQtc3R1ZGVudC1jbGFpbXNdIFRva2VuIHZlcmlmaWNhdGlvbiBmYWlsZWQ6JywgZXJyb3IpO1xyXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXHJcbiAgICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdJbnZhbGlkIElEIHRva2VuJyB9LFxyXG4gICAgICAgIHsgc3RhdHVzOiA0MDEgfVxyXG4gICAgICApO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHVpZCA9IGRlY29kZWRUb2tlbi51aWQ7XHJcblxyXG4gICAgLy8gU2V0IGN1c3RvbSBjbGFpbXMgZm9yIHRoZSB1c2VyXHJcbiAgICBjb25zdCBjdXN0b21DbGFpbXMgPSB7XHJcbiAgICAgIHJvbGU6ICdzdHVkZW50JyxcclxuICAgICAgc3R1ZGVudF9pZDogc3R1ZGVudElkXHJcbiAgICB9O1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGF3YWl0IGZpcmViYXNlQWRtaW5BdXRoLnNldEN1c3RvbVVzZXJDbGFpbXModWlkLCBjdXN0b21DbGFpbXMpO1xyXG4gICAgICBjb25zb2xlLmxvZyhgW3NldC1zdHVkZW50LWNsYWltc10gU3VjY2Vzc2Z1bGx5IHNldCBjbGFpbXMgZm9yIHVzZXIgJHt1aWR9LCBzdHVkZW50IElEOiAke3N0dWRlbnRJZH1gKTtcclxuICAgICAgXHJcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XHJcbiAgICAgICAgc3VjY2VzczogdHJ1ZSxcclxuICAgICAgICBtZXNzYWdlOiAnU3R1ZGVudCBjbGFpbXMgc2V0IHN1Y2Nlc3NmdWxseScsXHJcbiAgICAgICAgY2xhaW1zOiBjdXN0b21DbGFpbXNcclxuICAgICAgfSk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdbc2V0LXN0dWRlbnQtY2xhaW1zXSBFcnJvciBzZXR0aW5nIGN1c3RvbSBjbGFpbXM6JywgZXJyb3IpO1xyXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXHJcbiAgICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdGYWlsZWQgdG8gc2V0IGN1c3RvbSBjbGFpbXMnIH0sXHJcbiAgICAgICAgeyBzdGF0dXM6IDUwMCB9XHJcbiAgICAgICk7XHJcbiAgICB9XHJcblxyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKCdbc2V0LXN0dWRlbnQtY2xhaW1zXSBVbmV4cGVjdGVkIGVycm9yOicsIGVycm9yKTtcclxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcclxuICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdJbnRlcm5hbCBzZXJ2ZXIgZXJyb3InIH0sXHJcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxyXG4gICAgKTtcclxuICB9XHJcbn1cclxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImFkbWluIiwiUE9TVCIsInJlcXVlc3QiLCJhdXRoSGVhZGVyIiwiaGVhZGVycyIsImdldCIsInN0YXJ0c1dpdGgiLCJqc29uIiwic3VjY2VzcyIsImVycm9yIiwic3RhdHVzIiwiaWRUb2tlbiIsInJlcGxhY2UiLCJzdHVkZW50SWQiLCJhcHAiLCJyZXF1aXJlZFZhcnMiLCJtaXNzaW5nVmFycyIsImZpbHRlciIsInZhck5hbWUiLCJwcm9jZXNzIiwiZW52IiwibGVuZ3RoIiwiY29uc29sZSIsInByaXZhdGVLZXkiLCJGSVJFQkFTRV9QUklWQVRFX0tFWSIsImVuZHNXaXRoIiwic2xpY2UiLCJsb2ciLCJzdWJzdHJpbmciLCJzZXJ2aWNlQWNjb3VudCIsIkZJUkVCQVNFX1BST0pFQ1RfSUQiLCJGSVJFQkFTRV9QUklWQVRFX0tFWV9JRCIsIkZJUkVCQVNFX0NMSUVOVF9FTUFJTCIsIkZJUkVCQVNFX0NMSUVOVF9JRCIsInByb2plY3RfaWQiLCJjbGllbnRfZW1haWwiLCJhcHBOYW1lIiwiRGF0ZSIsIm5vdyIsIk1hdGgiLCJyYW5kb20iLCJ0b1N0cmluZyIsImV4aXN0aW5nQXBwcyIsImFwcHMiLCJmb3JFYWNoIiwiZXhpc3RpbmdBcHAiLCJuYW1lIiwiZGVsZXRlQXBwIiwiY2xlYW51cEVycm9yIiwid2FybiIsImluaXRpYWxpemVBcHAiLCJjcmVkZW50aWFsIiwiY2VydCIsInByb2plY3RJZCIsImluaXRFcnJvciIsImZpcmViYXNlQWRtaW5BdXRoIiwiYXV0aCIsImRlY29kZWRUb2tlbiIsInZlcmlmeUlkVG9rZW4iLCJ1aWQiLCJjdXN0b21DbGFpbXMiLCJyb2xlIiwic3R1ZGVudF9pZCIsInNldEN1c3RvbVVzZXJDbGFpbXMiLCJtZXNzYWdlIiwiY2xhaW1zIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/set-student-claims/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@opentelemetry/api":
/*!*************************************!*\
  !*** external "@opentelemetry/api" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@opentelemetry/api");

/***/ }),

/***/ "firebase-admin":
/*!*********************************!*\
  !*** external "firebase-admin" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("firebase-admin");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fset-student-claims%2Froute&page=%2Fapi%2Fauth%2Fset-student-claims%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fset-student-claims%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();