#!/usr/bin/env python3
"""
Test script to validate the specific fixes made to the lesson manager system:
1. Teaching completion logic enforces 100% objectives coverage
2. Minimum interaction count requirement removed
3. AI response duplication issue fixed
4. Phase state synchronization improved
"""

import sys
import os
import time
import json
from typing import Dict, Any, <PERSON><PERSON>

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_teaching_completion_logic():
    """Test that teaching completion logic enforces 100% objectives coverage"""
    print("🔧 Testing teaching completion logic...")
    
    try:
        from teaching_rules import TeachingRulesEngine
        
        engine = TeachingRulesEngine()
        
        # Test case 1: 100% objectives coverage should complete
        session_data_100 = {
            'objectives_tracking': {
                'completion_percentage': 100.0,
                'total_objectives': 5,
                'objectives_status': {
                    'objective_1': {'covered': True},
                    'objective_2': {'covered': True},
                    'objective_3': {'covered': True},
                    'objective_4': {'covered': True},
                    'objective_5': {'covered': True}
                }
            }
        }

        context_100 = {
            'grade': 'Primary 5',
            'teaching_level': 5,
            'lesson_complexity': 'moderate',
            'teaching_interactions': 5,
            'content_depth_score': 0.8
        }
        
        is_complete_100, reason_100, details_100 = engine.validate_teaching_completion(session_data_100, context_100)
        
        # Test case 2: 85% objectives coverage should NOT complete (even with many interactions)
        session_data_85 = {
            'objectives_tracking': {
                'completion_percentage': 85.0,
                'total_objectives': 5,
                'objectives_status': {
                    'objective_1': {'covered': True},
                    'objective_2': {'covered': True},
                    'objective_3': {'covered': True},
                    'objective_4': {'covered': True},
                    'objective_5': {'covered': False}  # One objective not covered
                }
            }
        }

        context_85 = {
            'grade': 'Primary 5',
            'teaching_level': 5,
            'lesson_complexity': 'moderate',
            'teaching_interactions': 20,  # Many interactions
            'content_depth_score': 0.9
        }
        
        is_complete_85, reason_85, details_85 = engine.validate_teaching_completion(session_data_85, context_85)
        
        print(f"   📊 100% objectives: Complete={is_complete_100}, Reason={reason_100}")
        print(f"   📊 85% objectives: Complete={is_complete_85}, Reason={reason_85}")
        
        # Validate results
        if is_complete_100 and not is_complete_85:
            print("   ✅ Teaching completion logic working correctly")
            return True
        else:
            print("   ❌ Teaching completion logic has issues")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing teaching completion: {e}")
        return False

def test_interaction_requirement_removed():
    """Test that minimum interaction count requirement is removed"""
    print("🔧 Testing interaction requirement removal...")
    
    try:
        from teaching_rules import TeachingRulesEngine
        
        engine = TeachingRulesEngine()
        
        # Test case: 100% objectives with minimal interactions should complete
        session_data_minimal = {
            'objectives_tracking': {
                'completion_percentage': 100.0,
                'total_objectives': 3,
                'objectives_status': {
                    'objective_1': {'covered': True},
                    'objective_2': {'covered': True},
                    'objective_3': {'covered': True}
                }
            }
        }

        context_minimal = {
            'grade': 'Primary 5',
            'teaching_level': 3,
            'lesson_complexity': 'simple',
            'teaching_interactions': 2,  # Very few interactions
            'content_depth_score': 0.7
        }
        
        is_complete, reason, details = engine.validate_teaching_completion(session_data_minimal, context_minimal)
        
        print(f"   📊 Minimal interactions with 100% objectives: Complete={is_complete}")
        print(f"   📊 Reason: {reason}")
        
        if is_complete and "100_percent_objectives" in reason:
            print("   ✅ Interaction requirement successfully removed")
            return True
        else:
            print("   ❌ Interaction requirement still present")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing interaction requirement: {e}")
        return False

def test_ai_response_duplication_fix():
    """Test that AI response duplication is fixed by checking main.py code"""
    print("🔧 Testing AI response duplication fix...")
    
    try:
        # Read main.py and check for the problematic appending code
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check that problematic text appending is removed/commented
        problematic_patterns = [
            'enhanced_content_text += "\\n\\nWe\'re making good progress!',
            'enhanced_content_text += "\\n\\nLet\'s focus on the most important',
            'enhanced_content_text += "\\n\\nLet\'s continue exploring this topic'
        ]
        
        issues_found = []
        for pattern in problematic_patterns:
            if pattern in content and not ('# REMOVED:' in content or 'REMOVED:' in content):
                issues_found.append(pattern)
        
        if not issues_found:
            print("   ✅ AI response duplication fixes applied correctly")
            return True
        else:
            print(f"   ❌ Found {len(issues_found)} potential duplication issues")
            for issue in issues_found:
                print(f"      - {issue[:50]}...")
            return False
            
    except Exception as e:
        print(f"   ❌ Error checking duplication fix: {e}")
        return False

def test_phase_synchronization():
    """Test that phase synchronization logic is present"""
    print("🔧 Testing phase synchronization...")
    
    try:
        # Check that main.py has proper phase synchronization
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for key synchronization elements
        sync_elements = [
            'final_phase_to_save',
            'current_phase_for_ai',
            'phase_transition_manager',
            'validate_ai_state_update'
        ]
        
        found_elements = []
        for element in sync_elements:
            if element in content:
                found_elements.append(element)
        
        print(f"   📊 Found {len(found_elements)}/{len(sync_elements)} synchronization elements")
        
        if len(found_elements) >= 3:  # At least 3 out of 4 elements should be present
            print("   ✅ Phase synchronization logic present")
            return True
        else:
            print("   ❌ Phase synchronization logic incomplete")
            return False
            
    except Exception as e:
        print(f"   ❌ Error checking phase synchronization: {e}")
        return False

def main():
    """Run all validation tests"""
    print("🚀 LESSON MANAGER FIXES VALIDATION")
    print("=" * 50)
    
    start_time = time.time()
    
    # Run all tests
    tests = [
        ("Teaching Completion Logic", test_teaching_completion_logic),
        ("Interaction Requirement Removal", test_interaction_requirement_removed),
        ("AI Response Duplication Fix", test_ai_response_duplication_fix),
        ("Phase Synchronization", test_phase_synchronization)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}")
        print("-" * 30)
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n📊 VALIDATION SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 OVERALL RESULT: {passed}/{total} tests passed")
    
    elapsed_time = time.time() - start_time
    print(f"⏱️  Total validation time: {elapsed_time:.2f}s")
    
    if passed == total:
        print("\n🎉 ALL FIXES VALIDATED SUCCESSFULLY!")
        print("✅ The lesson manager system fixes are working correctly")
        return True
    else:
        print(f"\n⚠️  {total - passed} ISSUES DETECTED")
        print("❌ Some fixes need attention")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
