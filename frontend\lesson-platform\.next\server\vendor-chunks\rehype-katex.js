"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-katex";
exports.ids = ["vendor-chunks/rehype-katex"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype-katex/lib/index.js":
/*!************************************************!*\
  !*** ./node_modules/rehype-katex/lib/index.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rehypeKatex)\n/* harmony export */ });\n/* harmony import */ var hast_util_from_html_isomorphic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-from-html-isomorphic */ \"(ssr)/./node_modules/hast-util-from-html-isomorphic/lib/index.js\");\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/./node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var katex__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! katex */ \"(ssr)/./node_modules/katex/dist/katex.mjs\");\n/* harmony import */ var unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit-parents */ \"(ssr)/./node_modules/unist-util-visit-parents/lib/index.js\");\n/**\n * @import {ElementContent, Root} from 'hast'\n * @import {KatexOptions} from 'katex'\n * @import {VFile} from 'vfile'\n */\n\n/**\n * @typedef {Omit<KatexOptions, 'displayMode' | 'throwOnError'>} Options\n */\n\n\n\n\n\n\n/** @type {Readonly<Options>} */\nconst emptyOptions = {}\n/** @type {ReadonlyArray<unknown>} */\nconst emptyClasses = []\n\n/**\n * Render elements with a `language-math` (or `math-display`, `math-inline`)\n * class with KaTeX.\n *\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns\n *   Transform.\n */\nfunction rehypeKatex(options) {\n  const settings = options || emptyOptions\n\n  /**\n   * Transform.\n   *\n   * @param {Root} tree\n   *   Tree.\n   * @param {VFile} file\n   *   File.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  return function (tree, file) {\n    ;(0,unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_1__.visitParents)(tree, 'element', function (element, parents) {\n      const classes = Array.isArray(element.properties.className)\n        ? element.properties.className\n        : emptyClasses\n      // This class can be generated from markdown with ` ```math `.\n      const languageMath = classes.includes('language-math')\n      // This class is used by `remark-math` for flow math (block, `$$\\nmath\\n$$`).\n      const mathDisplay = classes.includes('math-display')\n      // This class is used by `remark-math` for text math (inline, `$math$`).\n      const mathInline = classes.includes('math-inline')\n      let displayMode = mathDisplay\n\n      // Any class is fine.\n      if (!languageMath && !mathDisplay && !mathInline) {\n        return\n      }\n\n      let parent = parents[parents.length - 1]\n      let scope = element\n\n      // If this was generated with ` ```math `, replace the `<pre>` and use\n      // display.\n      if (\n        element.tagName === 'code' &&\n        languageMath &&\n        parent &&\n        parent.type === 'element' &&\n        parent.tagName === 'pre'\n      ) {\n        scope = parent\n        parent = parents[parents.length - 2]\n        displayMode = true\n      }\n\n      /* c8 ignore next -- verbose to test. */\n      if (!parent) return\n\n      const value = (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_2__.toText)(scope, {whitespace: 'pre'})\n\n      /** @type {Array<ElementContent> | string | undefined} */\n      let result\n\n      try {\n        result = katex__WEBPACK_IMPORTED_MODULE_0__[\"default\"].renderToString(value, {\n          ...settings,\n          displayMode,\n          throwOnError: true\n        })\n      } catch (error) {\n        const cause = /** @type {Error} */ (error)\n        const ruleId = cause.name.toLowerCase()\n\n        file.message('Could not render math with KaTeX', {\n          ancestors: [...parents, element],\n          cause,\n          place: element.position,\n          ruleId,\n          source: 'rehype-katex'\n        })\n\n        // KaTeX *should* handle `ParseError` itself, but not others.\n        // it doesn’t always:\n        // <https://github.com/remarkjs/react-markdown/issues/853>\n        try {\n          result = katex__WEBPACK_IMPORTED_MODULE_0__[\"default\"].renderToString(value, {\n            ...settings,\n            displayMode,\n            strict: 'ignore',\n            throwOnError: false\n          })\n        } catch {\n          // Generate similar markup if this is an other error.\n          // See: <https://github.com/KaTeX/KaTeX/blob/5dc7af0/docs/error.md>.\n          result = [\n            {\n              type: 'element',\n              tagName: 'span',\n              properties: {\n                className: ['katex-error'],\n                style: 'color:' + (settings.errorColor || '#cc0000'),\n                title: String(error)\n              },\n              children: [{type: 'text', value}]\n            }\n          ]\n        }\n      }\n\n      if (typeof result === 'string') {\n        const root = (0,hast_util_from_html_isomorphic__WEBPACK_IMPORTED_MODULE_3__.fromHtmlIsomorphic)(result, {fragment: true})\n        // Cast as we don’t expect `doctypes` in KaTeX result.\n        result = /** @type {Array<ElementContent>} */ (root.children)\n      }\n\n      const index = parent.children.indexOf(scope)\n      parent.children.splice(index, 1, ...result)\n      return unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_1__.SKIP\n    })\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVoeXBlLWthdGV4L2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQ0EsWUFBWSxzQkFBc0I7QUFDbEMsWUFBWSxjQUFjO0FBQzFCLFlBQVksT0FBTztBQUNuQjs7QUFFQTtBQUNBLGFBQWEsb0RBQW9EO0FBQ2pFOztBQUVpRTtBQUN6QjtBQUNmO0FBQ2tDOztBQUUzRCxXQUFXLG1CQUFtQjtBQUM5QjtBQUNBLFdBQVcsd0JBQXdCO0FBQ25DOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxzQ0FBc0M7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDZTtBQUNmOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsTUFBTTtBQUNuQjtBQUNBLGFBQWEsT0FBTztBQUNwQjtBQUNBLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQSxJQUFJLHVFQUFZO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSxvQkFBb0IseURBQU0sU0FBUyxrQkFBa0I7O0FBRXJELGlCQUFpQiw0Q0FBNEM7QUFDN0Q7O0FBRUE7QUFDQSxpQkFBaUIsNERBQW9CO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxRQUFRO0FBQ1IsaUNBQWlDLE9BQU87QUFDeEM7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUzs7QUFFVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiw0REFBb0I7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1gsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZTtBQUNmLDBCQUEwQixvQkFBb0I7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxxQkFBcUIsa0ZBQWtCLFVBQVUsZUFBZTtBQUNoRTtBQUNBLDRCQUE0Qix1QkFBdUI7QUFDbkQ7O0FBRUE7QUFDQTtBQUNBLGFBQWEsMERBQUk7QUFDakIsS0FBSztBQUNMO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xccmVoeXBlLWthdGV4XFxsaWJcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7RWxlbWVudENvbnRlbnQsIFJvb3R9IGZyb20gJ2hhc3QnXG4gKiBAaW1wb3J0IHtLYXRleE9wdGlvbnN9IGZyb20gJ2thdGV4J1xuICogQGltcG9ydCB7VkZpbGV9IGZyb20gJ3ZmaWxlJ1xuICovXG5cbi8qKlxuICogQHR5cGVkZWYge09taXQ8S2F0ZXhPcHRpb25zLCAnZGlzcGxheU1vZGUnIHwgJ3Rocm93T25FcnJvcic+fSBPcHRpb25zXG4gKi9cblxuaW1wb3J0IHtmcm9tSHRtbElzb21vcnBoaWN9IGZyb20gJ2hhc3QtdXRpbC1mcm9tLWh0bWwtaXNvbW9ycGhpYydcbmltcG9ydCB7dG9UZXh0fSBmcm9tICdoYXN0LXV0aWwtdG8tdGV4dCdcbmltcG9ydCBrYXRleCBmcm9tICdrYXRleCdcbmltcG9ydCB7U0tJUCwgdmlzaXRQYXJlbnRzfSBmcm9tICd1bmlzdC11dGlsLXZpc2l0LXBhcmVudHMnXG5cbi8qKiBAdHlwZSB7UmVhZG9ubHk8T3B0aW9ucz59ICovXG5jb25zdCBlbXB0eU9wdGlvbnMgPSB7fVxuLyoqIEB0eXBlIHtSZWFkb25seUFycmF5PHVua25vd24+fSAqL1xuY29uc3QgZW1wdHlDbGFzc2VzID0gW11cblxuLyoqXG4gKiBSZW5kZXIgZWxlbWVudHMgd2l0aCBhIGBsYW5ndWFnZS1tYXRoYCAob3IgYG1hdGgtZGlzcGxheWAsIGBtYXRoLWlubGluZWApXG4gKiBjbGFzcyB3aXRoIEthVGVYLlxuICpcbiAqIEBwYXJhbSB7UmVhZG9ubHk8T3B0aW9ucz4gfCBudWxsIHwgdW5kZWZpbmVkfSBbb3B0aW9uc11cbiAqICAgQ29uZmlndXJhdGlvbiAob3B0aW9uYWwpLlxuICogQHJldHVybnNcbiAqICAgVHJhbnNmb3JtLlxuICovXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiByZWh5cGVLYXRleChvcHRpb25zKSB7XG4gIGNvbnN0IHNldHRpbmdzID0gb3B0aW9ucyB8fCBlbXB0eU9wdGlvbnNcblxuICAvKipcbiAgICogVHJhbnNmb3JtLlxuICAgKlxuICAgKiBAcGFyYW0ge1Jvb3R9IHRyZWVcbiAgICogICBUcmVlLlxuICAgKiBAcGFyYW0ge1ZGaWxlfSBmaWxlXG4gICAqICAgRmlsZS5cbiAgICogQHJldHVybnMge3VuZGVmaW5lZH1cbiAgICogICBOb3RoaW5nLlxuICAgKi9cbiAgcmV0dXJuIGZ1bmN0aW9uICh0cmVlLCBmaWxlKSB7XG4gICAgdmlzaXRQYXJlbnRzKHRyZWUsICdlbGVtZW50JywgZnVuY3Rpb24gKGVsZW1lbnQsIHBhcmVudHMpIHtcbiAgICAgIGNvbnN0IGNsYXNzZXMgPSBBcnJheS5pc0FycmF5KGVsZW1lbnQucHJvcGVydGllcy5jbGFzc05hbWUpXG4gICAgICAgID8gZWxlbWVudC5wcm9wZXJ0aWVzLmNsYXNzTmFtZVxuICAgICAgICA6IGVtcHR5Q2xhc3Nlc1xuICAgICAgLy8gVGhpcyBjbGFzcyBjYW4gYmUgZ2VuZXJhdGVkIGZyb20gbWFya2Rvd24gd2l0aCBgIGBgYG1hdGggYC5cbiAgICAgIGNvbnN0IGxhbmd1YWdlTWF0aCA9IGNsYXNzZXMuaW5jbHVkZXMoJ2xhbmd1YWdlLW1hdGgnKVxuICAgICAgLy8gVGhpcyBjbGFzcyBpcyB1c2VkIGJ5IGByZW1hcmstbWF0aGAgZm9yIGZsb3cgbWF0aCAoYmxvY2ssIGAkJFxcbm1hdGhcXG4kJGApLlxuICAgICAgY29uc3QgbWF0aERpc3BsYXkgPSBjbGFzc2VzLmluY2x1ZGVzKCdtYXRoLWRpc3BsYXknKVxuICAgICAgLy8gVGhpcyBjbGFzcyBpcyB1c2VkIGJ5IGByZW1hcmstbWF0aGAgZm9yIHRleHQgbWF0aCAoaW5saW5lLCBgJG1hdGgkYCkuXG4gICAgICBjb25zdCBtYXRoSW5saW5lID0gY2xhc3Nlcy5pbmNsdWRlcygnbWF0aC1pbmxpbmUnKVxuICAgICAgbGV0IGRpc3BsYXlNb2RlID0gbWF0aERpc3BsYXlcblxuICAgICAgLy8gQW55IGNsYXNzIGlzIGZpbmUuXG4gICAgICBpZiAoIWxhbmd1YWdlTWF0aCAmJiAhbWF0aERpc3BsYXkgJiYgIW1hdGhJbmxpbmUpIHtcbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG5cbiAgICAgIGxldCBwYXJlbnQgPSBwYXJlbnRzW3BhcmVudHMubGVuZ3RoIC0gMV1cbiAgICAgIGxldCBzY29wZSA9IGVsZW1lbnRcblxuICAgICAgLy8gSWYgdGhpcyB3YXMgZ2VuZXJhdGVkIHdpdGggYCBgYGBtYXRoIGAsIHJlcGxhY2UgdGhlIGA8cHJlPmAgYW5kIHVzZVxuICAgICAgLy8gZGlzcGxheS5cbiAgICAgIGlmIChcbiAgICAgICAgZWxlbWVudC50YWdOYW1lID09PSAnY29kZScgJiZcbiAgICAgICAgbGFuZ3VhZ2VNYXRoICYmXG4gICAgICAgIHBhcmVudCAmJlxuICAgICAgICBwYXJlbnQudHlwZSA9PT0gJ2VsZW1lbnQnICYmXG4gICAgICAgIHBhcmVudC50YWdOYW1lID09PSAncHJlJ1xuICAgICAgKSB7XG4gICAgICAgIHNjb3BlID0gcGFyZW50XG4gICAgICAgIHBhcmVudCA9IHBhcmVudHNbcGFyZW50cy5sZW5ndGggLSAyXVxuICAgICAgICBkaXNwbGF5TW9kZSA9IHRydWVcbiAgICAgIH1cblxuICAgICAgLyogYzggaWdub3JlIG5leHQgLS0gdmVyYm9zZSB0byB0ZXN0LiAqL1xuICAgICAgaWYgKCFwYXJlbnQpIHJldHVyblxuXG4gICAgICBjb25zdCB2YWx1ZSA9IHRvVGV4dChzY29wZSwge3doaXRlc3BhY2U6ICdwcmUnfSlcblxuICAgICAgLyoqIEB0eXBlIHtBcnJheTxFbGVtZW50Q29udGVudD4gfCBzdHJpbmcgfCB1bmRlZmluZWR9ICovXG4gICAgICBsZXQgcmVzdWx0XG5cbiAgICAgIHRyeSB7XG4gICAgICAgIHJlc3VsdCA9IGthdGV4LnJlbmRlclRvU3RyaW5nKHZhbHVlLCB7XG4gICAgICAgICAgLi4uc2V0dGluZ3MsXG4gICAgICAgICAgZGlzcGxheU1vZGUsXG4gICAgICAgICAgdGhyb3dPbkVycm9yOiB0cnVlXG4gICAgICAgIH0pXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zdCBjYXVzZSA9IC8qKiBAdHlwZSB7RXJyb3J9ICovIChlcnJvcilcbiAgICAgICAgY29uc3QgcnVsZUlkID0gY2F1c2UubmFtZS50b0xvd2VyQ2FzZSgpXG5cbiAgICAgICAgZmlsZS5tZXNzYWdlKCdDb3VsZCBub3QgcmVuZGVyIG1hdGggd2l0aCBLYVRlWCcsIHtcbiAgICAgICAgICBhbmNlc3RvcnM6IFsuLi5wYXJlbnRzLCBlbGVtZW50XSxcbiAgICAgICAgICBjYXVzZSxcbiAgICAgICAgICBwbGFjZTogZWxlbWVudC5wb3NpdGlvbixcbiAgICAgICAgICBydWxlSWQsXG4gICAgICAgICAgc291cmNlOiAncmVoeXBlLWthdGV4J1xuICAgICAgICB9KVxuXG4gICAgICAgIC8vIEthVGVYICpzaG91bGQqIGhhbmRsZSBgUGFyc2VFcnJvcmAgaXRzZWxmLCBidXQgbm90IG90aGVycy5cbiAgICAgICAgLy8gaXQgZG9lc27igJl0IGFsd2F5czpcbiAgICAgICAgLy8gPGh0dHBzOi8vZ2l0aHViLmNvbS9yZW1hcmtqcy9yZWFjdC1tYXJrZG93bi9pc3N1ZXMvODUzPlxuICAgICAgICB0cnkge1xuICAgICAgICAgIHJlc3VsdCA9IGthdGV4LnJlbmRlclRvU3RyaW5nKHZhbHVlLCB7XG4gICAgICAgICAgICAuLi5zZXR0aW5ncyxcbiAgICAgICAgICAgIGRpc3BsYXlNb2RlLFxuICAgICAgICAgICAgc3RyaWN0OiAnaWdub3JlJyxcbiAgICAgICAgICAgIHRocm93T25FcnJvcjogZmFsc2VcbiAgICAgICAgICB9KVxuICAgICAgICB9IGNhdGNoIHtcbiAgICAgICAgICAvLyBHZW5lcmF0ZSBzaW1pbGFyIG1hcmt1cCBpZiB0aGlzIGlzIGFuIG90aGVyIGVycm9yLlxuICAgICAgICAgIC8vIFNlZTogPGh0dHBzOi8vZ2l0aHViLmNvbS9LYVRlWC9LYVRlWC9ibG9iLzVkYzdhZjAvZG9jcy9lcnJvci5tZD4uXG4gICAgICAgICAgcmVzdWx0ID0gW1xuICAgICAgICAgICAge1xuICAgICAgICAgICAgICB0eXBlOiAnZWxlbWVudCcsXG4gICAgICAgICAgICAgIHRhZ05hbWU6ICdzcGFuJyxcbiAgICAgICAgICAgICAgcHJvcGVydGllczoge1xuICAgICAgICAgICAgICAgIGNsYXNzTmFtZTogWydrYXRleC1lcnJvciddLFxuICAgICAgICAgICAgICAgIHN0eWxlOiAnY29sb3I6JyArIChzZXR0aW5ncy5lcnJvckNvbG9yIHx8ICcjY2MwMDAwJyksXG4gICAgICAgICAgICAgICAgdGl0bGU6IFN0cmluZyhlcnJvcilcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgY2hpbGRyZW46IFt7dHlwZTogJ3RleHQnLCB2YWx1ZX1dXG4gICAgICAgICAgICB9XG4gICAgICAgICAgXVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGlmICh0eXBlb2YgcmVzdWx0ID09PSAnc3RyaW5nJykge1xuICAgICAgICBjb25zdCByb290ID0gZnJvbUh0bWxJc29tb3JwaGljKHJlc3VsdCwge2ZyYWdtZW50OiB0cnVlfSlcbiAgICAgICAgLy8gQ2FzdCBhcyB3ZSBkb27igJl0IGV4cGVjdCBgZG9jdHlwZXNgIGluIEthVGVYIHJlc3VsdC5cbiAgICAgICAgcmVzdWx0ID0gLyoqIEB0eXBlIHtBcnJheTxFbGVtZW50Q29udGVudD59ICovIChyb290LmNoaWxkcmVuKVxuICAgICAgfVxuXG4gICAgICBjb25zdCBpbmRleCA9IHBhcmVudC5jaGlsZHJlbi5pbmRleE9mKHNjb3BlKVxuICAgICAgcGFyZW50LmNoaWxkcmVuLnNwbGljZShpbmRleCwgMSwgLi4ucmVzdWx0KVxuICAgICAgcmV0dXJuIFNLSVBcbiAgICB9KVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-katex/lib/index.js\n");

/***/ })

};
;