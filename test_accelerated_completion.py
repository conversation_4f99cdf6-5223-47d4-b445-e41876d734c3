#!/usr/bin/env python3
"""
Quick test to verify accelerated teaching completion logic is working.
"""

import requests
import json

def test_accelerated_completion():
    """Test the accelerated completion logic with 8-10 interactions."""
    
    base_url = "http://localhost:5000"
    
    # Get test token
    token_response = requests.get(f"{base_url}/generate-test-token")
    if token_response.status_code != 200:
        print("❌ Failed to get test token")
        return False
    
    token = token_response.json()["token"]
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
        "X-Student-Id": "andrea_ugono_33305",
        "X-Testing-Mode": "true"
    }
    
    # Initialize lesson session
    lesson_data = {
        "student_id": "andrea_ugono_33305",
        "lessonRef": "P5-MAT-001",
        "country": "Nigeria",
        "curriculum": "National Curriculum",
        "grade": "Primary 5",
        "level": "P5",
        "subject": "Mathematics",
        "content_to_enhance": "Start diagnostic assessment",
        "chat_history": []
    }
    
    print("🎯 Testing Accelerated Teaching Completion")
    print("=" * 50)
    
    # Initialize session
    response = requests.post(f"{base_url}/api/enhance-content", headers=headers, json=lesson_data)
    if response.status_code != 200:
        print("❌ Failed to initialize lesson session")
        return False
    
    session_data = response.json()
    session_id = session_data.get("session_id")
    print(f"✅ Session initialized: {session_id}")
    
    # Skip diagnostic phase by simulating completion
    for i in range(6):
        enhance_data = {
            "student_id": "andrea_ugono_33305",
            "session_id": session_id,
            "content_to_enhance": f"Diagnostic answer {i+1}",
            "chat_history": []
        }
        
        response = requests.post(f"{base_url}/api/enhance-content", headers=headers, json=enhance_data)
        if response.status_code != 200:
            print(f"❌ Failed at diagnostic {i+1}")
            continue
        
        result = response.json()
        current_phase = result.get("current_lesson_phase", "unknown")
        print(f"📝 Diagnostic {i+1}: {current_phase}")
        
        if current_phase.startswith("teaching"):
            print(f"✅ Diagnostic completed, moved to teaching phase")
            break
    
    # Test accelerated teaching completion at 8 interactions
    print("\n🎓 Testing Teaching Phase with Accelerated Completion")
    print("-" * 50)
    
    for i in range(12):  # Test up to 12 interactions
        enhance_data = {
            "student_id": "andrea_ugono_33305",
            "session_id": session_id,
            "content_to_enhance": f"Continue teaching interaction {i+1}",
            "chat_history": []
        }
        
        response = requests.post(f"{base_url}/api/enhance-content", headers=headers, json=enhance_data)
        if response.status_code != 200:
            print(f"❌ Failed at teaching interaction {i+1}")
            continue
        
        result = response.json()
        current_phase = result.get("current_lesson_phase", "unknown")
        teaching_interactions = result.get("teaching_interactions", 0)
        coverage_pct = result.get("coverage_percentage", 0)
        
        print(f"📖 Teaching {i+1}: Phase={current_phase}, Interactions={teaching_interactions}, Coverage={coverage_pct:.1f}%")
        
        # Check for quiz transition
        if current_phase == "quiz_initiate":
            print(f"🎯 SUCCESS! Quiz transition at teaching interaction {i+1}")
            print(f"📊 Final stats: {teaching_interactions} interactions, {coverage_pct:.1f}% coverage")
            return True
        
        # Check accelerated completion criteria
        if i >= 7:  # 8+ interactions
            if coverage_pct >= 85.0:
                print(f"🎯 EXPECTED: Should transition to quiz (8+ interactions + 85%+ coverage)")
            elif i >= 9:  # 10+ interactions
                print(f"🎯 EXPECTED: Should transition to quiz (10+ interactions)")
    
    print("❌ No quiz transition detected after 12 teaching interactions")
    return False

if __name__ == "__main__":
    success = test_accelerated_completion()
    exit(0 if success else 1)
