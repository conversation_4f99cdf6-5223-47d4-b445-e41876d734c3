{"..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts": {"id": "..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts", "files": ["static/css/vendors.css", "static/chunks/vendors.js"]}, "lib\\firebase.ts -> firebase/auth": {"id": "lib\\firebase.ts -> firebase/auth", "files": []}, "lib\\firebase.ts -> firebase/firestore": {"id": "lib\\firebase.ts -> firebase/firestore", "files": []}, "lib\\firebase.ts -> firebase/storage": {"id": "lib\\firebase.ts -> firebase/storage", "files": []}, "services\\LessonProgressService.ts -> @/lib/firebase": {"id": "services\\LessonProgressService.ts -> @/lib/firebase", "files": []}}