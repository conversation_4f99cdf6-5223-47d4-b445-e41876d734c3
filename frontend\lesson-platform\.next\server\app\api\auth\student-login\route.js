/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/student-login/route";
exports.ids = ["app/api/auth/student-login/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fstudent-login%2Froute&page=%2Fapi%2Fauth%2Fstudent-login%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fstudent-login%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fstudent-login%2Froute&page=%2Fapi%2Fauth%2Fstudent-login%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fstudent-login%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_auth_student_login_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/student-login/route.ts */ \"(rsc)/./src/app/api/auth/student-login/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/student-login/route\",\n        pathname: \"/api/auth/student-login\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/student-login/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\api\\\\auth\\\\student-login\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_auth_student_login_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZhdXRoJTJGc3R1ZGVudC1sb2dpbiUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGYXV0aCUyRnN0dWRlbnQtbG9naW4lMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZhdXRoJTJGc3R1ZGVudC1sb2dpbiUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNwYyU1Q09uZURyaXZlJTVDRGVza3RvcCU1Q0Rlc2t0b3AlNUNTb2x5bnRhX1dlYnNpdGUlNUNmcm9udGVuZCU1Q2xlc3Nvbi1wbGF0Zm9ybSU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDcGMlNUNPbmVEcml2ZSU1Q0Rlc2t0b3AlNUNEZXNrdG9wJTVDU29seW50YV9XZWJzaXRlJTVDZnJvbnRlbmQlNUNsZXNzb24tcGxhdGZvcm0maXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQStGO0FBQ3ZDO0FBQ3FCO0FBQ21GO0FBQ2hLO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix5R0FBbUI7QUFDM0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsc0RBQXNEO0FBQzlEO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQzBGOztBQUUxRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxwY1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXERlc2t0b3BcXFxcU29seW50YV9XZWJzaXRlXFxcXGZyb250ZW5kXFxcXGxlc3Nvbi1wbGF0Zm9ybVxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxhdXRoXFxcXHN0dWRlbnQtbG9naW5cXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2F1dGgvc3R1ZGVudC1sb2dpbi9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL2F1dGgvc3R1ZGVudC1sb2dpblwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvYXV0aC9zdHVkZW50LWxvZ2luL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiQzpcXFxcVXNlcnNcXFxccGNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEZXNrdG9wXFxcXFNvbHludGFfV2Vic2l0ZVxcXFxmcm9udGVuZFxcXFxsZXNzb24tcGxhdGZvcm1cXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcYXV0aFxcXFxzdHVkZW50LWxvZ2luXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fstudent-login%2Froute&page=%2Fapi%2Fauth%2Fstudent-login%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fstudent-login%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/student-login/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/student-login/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_security_PasswordService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../lib/security/PasswordService */ \"(rsc)/./src/lib/security/PasswordService.ts\");\n/* harmony import */ var firebase_admin__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase-admin */ \"firebase-admin\");\n/* harmony import */ var firebase_admin__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(firebase_admin__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json().catch(()=>{\n            throw new Error('Invalid request format');\n        });\n        const { studentId, password } = body;\n        if (!studentId || !password || password.trim() === '') {\n            const message = !studentId ? 'Student ID is required' : 'Password is required';\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message,\n                studentId: null,\n                token: null\n            }, {\n                status: 400\n            });\n        }\n        // Initialize Firebase Admin with explicit project ID\n        let app;\n        try {\n            // Validate environment variables first\n            const requiredVars = [\n                'FIREBASE_PROJECT_ID',\n                'FIREBASE_CLIENT_EMAIL',\n                'FIREBASE_PRIVATE_KEY_ID',\n                'FIREBASE_CLIENT_ID',\n                'FIREBASE_PRIVATE_KEY'\n            ];\n            const missingVars = requiredVars.filter((varName)=>!process.env[varName]);\n            if (missingVars.length > 0) {\n                console.error('[Student Login] Missing environment variables:', missingVars);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: 'Server configuration error',\n                    studentId: null,\n                    token: null\n                }, {\n                    status: 500\n                });\n            }\n            // Process the private key to handle different newline formats\n            let privateKey = process.env.FIREBASE_PRIVATE_KEY;\n            if (privateKey) {\n                // Remove surrounding quotes if they exist\n                if (privateKey.startsWith('\"') && privateKey.endsWith('\"')) {\n                    privateKey = privateKey.slice(1, -1);\n                }\n                // Handle different possible newline encodings\n                privateKey = privateKey.replace(/\\\\\\\\n/g, '\\n') // Handle double-escaped newlines\n                .replace(/\\\\n/g, '\\n'); // Handle escaped newlines\n                console.log('[Student Login] Processed private key length:', privateKey.length);\n                console.log('[Student Login] Private key starts with:', privateKey.substring(0, 30));\n            }\n            // Always create a fresh instance for this API route\n            const serviceAccount = {\n                \"type\": \"service_account\",\n                \"project_id\": process.env.FIREBASE_PROJECT_ID,\n                \"private_key_id\": process.env.FIREBASE_PRIVATE_KEY_ID,\n                \"private_key\": privateKey,\n                \"client_email\": process.env.FIREBASE_CLIENT_EMAIL,\n                \"client_id\": process.env.FIREBASE_CLIENT_ID,\n                \"auth_uri\": \"https://accounts.google.com/o/oauth2/auth\",\n                \"token_uri\": \"https://oauth2.googleapis.com/token\",\n                \"auth_provider_x509_cert_url\": \"https://www.googleapis.com/oauth2/v1/certs\",\n                \"client_x509_cert_url\": `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.FIREBASE_CLIENT_EMAIL?.replace('@', '%40')}`,\n                \"universe_domain\": \"googleapis.com\"\n            };\n            console.log('[Student Login] Initializing Firebase Admin...');\n            console.log('[Student Login] Project ID:', serviceAccount.project_id);\n            console.log('[Student Login] Client Email:', serviceAccount.client_email);\n            console.log('[Student Login] Private Key ID:', serviceAccount.private_key_id);\n            console.log('[Student Login] Private Key Length:', serviceAccount.private_key?.length);\n            // Create a unique app name for this route to avoid conflicts\n            const appName = `student-login-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;\n            // Clean up any existing apps with similar names to avoid conflicts\n            const existingApps = (firebase_admin__WEBPACK_IMPORTED_MODULE_2___default().apps);\n            existingApps.forEach((existingApp)=>{\n                if (existingApp && existingApp.name && existingApp.name.startsWith('student-login-')) {\n                    try {\n                        firebase_admin__WEBPACK_IMPORTED_MODULE_2___default().deleteApp(existingApp);\n                        console.log(`[Student Login] Cleaned up existing app: ${existingApp.name}`);\n                    } catch (cleanupError) {\n                        console.warn(`[Student Login] Could not clean up app ${existingApp.name}:`, cleanupError);\n                    }\n                }\n            });\n            app = firebase_admin__WEBPACK_IMPORTED_MODULE_2___default().initializeApp({\n                credential: firebase_admin__WEBPACK_IMPORTED_MODULE_2___default().credential.cert(serviceAccount),\n                projectId: serviceAccount.project_id\n            }, appName);\n            console.log('[Student Login] Firebase Admin initialized successfully with app name:', appName);\n        } catch (initError) {\n            console.error('[Student Login] Firebase initialization error:', initError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Authentication service unavailable',\n                studentId: null,\n                token: null\n            }, {\n                status: 500\n            });\n        }\n        const db = app.firestore();\n        console.log('Checking student:', studentId);\n        const studentDoc = await db.collection('students').doc(studentId).get();\n        console.log('Student doc exists:', studentDoc.exists);\n        if (!studentDoc.exists) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Invalid student ID or password',\n                studentId: null,\n                token: null\n            }, {\n                status: 401\n            });\n        }\n        const studentData = studentDoc.data();\n        console.log('Student data:', studentData);\n        if (!studentData?.password) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Account configuration error',\n                studentId: null,\n                token: null\n            }, {\n                status: 500\n            });\n        }\n        console.log('Password provided:', password);\n        console.log('Password stored:', studentData?.password);\n        const isPasswordValid = studentData.password.startsWith('$2') ? await _lib_security_PasswordService__WEBPACK_IMPORTED_MODULE_1__.PasswordService.verifyPassword(password, studentData.password) : password === studentData.password;\n        console.log('Password valid:', isPasswordValid);\n        if (!isPasswordValid) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Invalid password',\n                studentId: null,\n                token: null\n            }, {\n                status: 401\n            });\n        }\n        if (isPasswordValid && !studentData.password.startsWith('$2')) {\n            const hashedPassword = await _lib_security_PasswordService__WEBPACK_IMPORTED_MODULE_1__.PasswordService.hashPassword(password);\n            await db.collection('students').doc(studentId).update({\n                password: hashedPassword\n            }).catch((err)=>console.error('Password migration failed:', err));\n        }\n        const customClaims = {\n            student_id: studentId,\n            role: 'student',\n            name: studentData.name || 'Student'\n        };\n        const auth = app.auth();\n        const firebaseToken = await auth.createCustomToken(studentId, customClaims);\n        console.log('Generated Firebase token length:', firebaseToken.length);\n        console.log('Token starts with:', firebaseToken.substring(0, 50));\n        console.log('Token ends with:', firebaseToken.substring(firebaseToken.length - 50));\n        // Create a backend session document\n        const sessionId = `sess_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;\n        const sessionRef = db.collection('sessions').doc(sessionId);\n        await sessionRef.set({\n            studentId,\n            createdAt: new Date(),\n            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),\n            userAgent: request.headers.get('user-agent') || '',\n            ip: request.headers.get('x-forwarded-for') || ''\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Authentication successful',\n            id: studentId,\n            studentId,\n            token: firebaseToken,\n            sessionId,\n            name: studentData.name || 'Student',\n            email: studentData.email || null,\n            role: 'student'\n        }, {\n            status: 200\n        });\n    } catch (error) {\n        console.error('Authentication error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Server error',\n            studentId: null,\n            token: null\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/student-login/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/security/PasswordService.ts":
/*!*********************************************!*\
  !*** ./src/lib/security/PasswordService.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PasswordService: () => (/* binding */ PasswordService)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_1__);\n\n\nclass PasswordService {\n    static{\n        this.SALT_ROUNDS = 10;\n    }\n    /**\r\n   * Hash a password using bcryptjs\r\n   */ static async hashPassword(password) {\n        return bcryptjs__WEBPACK_IMPORTED_MODULE_0__.hash(password, this.SALT_ROUNDS);\n    }\n    /**\r\n   * Verify a password against a hash\r\n   */ static async verifyPassword(password, hash) {\n        try {\n            return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__.compare(password, hash);\n        } catch (error) {\n            console.error('Password verification error:', error);\n            return false;\n        }\n    }\n    /**\r\n   * Generate a secure random password\r\n   */ static generateRandomPassword(length = 10) {\n        return (0,crypto__WEBPACK_IMPORTED_MODULE_1__.randomBytes)(length).toString('hex').slice(0, length);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/security/PasswordService.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@opentelemetry/api":
/*!*************************************!*\
  !*** external "@opentelemetry/api" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@opentelemetry/api");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "firebase-admin":
/*!*********************************!*\
  !*** external "firebase-admin" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("firebase-admin");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/bcryptjs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fstudent-login%2Froute&page=%2Fapi%2Fauth%2Fstudent-login%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fstudent-login%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();