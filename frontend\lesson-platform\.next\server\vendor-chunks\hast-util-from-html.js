"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-from-html";
exports.ids = ["vendor-chunks/hast-util-from-html"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-from-html/lib/errors.js":
/*!********************************************************!*\
  !*** ./node_modules/hast-util-from-html/lib/errors.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   errors: () => (/* binding */ errors)\n/* harmony export */ });\n/**\n * @typedef ErrorInfo\n *   Info on a `parse5` error.\n * @property {string} reason\n *   Reason of error.\n * @property {string} description\n *   More info on error.\n * @property {false} [url]\n *   Turn off if this is not documented in the html5 spec (optional).\n */\n\nconst errors = {\n  /** @type {ErrorInfo} */\n  abandonedHeadElementChild: {\n    reason: 'Unexpected metadata element after head',\n    description:\n      'Unexpected element after head. Expected the element before `</head>`',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  abruptClosingOfEmptyComment: {\n    reason: 'Unexpected abruptly closed empty comment',\n    description: 'Unexpected `>` or `->`. Expected `-->` to close comments'\n  },\n  /** @type {ErrorInfo} */\n  abruptDoctypePublicIdentifier: {\n    reason: 'Unexpected abruptly closed public identifier',\n    description:\n      'Unexpected `>`. Expected a closing `\"` or `\\'` after the public identifier'\n  },\n  /** @type {ErrorInfo} */\n  abruptDoctypeSystemIdentifier: {\n    reason: 'Unexpected abruptly closed system identifier',\n    description:\n      'Unexpected `>`. Expected a closing `\"` or `\\'` after the identifier identifier'\n  },\n  /** @type {ErrorInfo} */\n  absenceOfDigitsInNumericCharacterReference: {\n    reason: 'Unexpected non-digit at start of numeric character reference',\n    description:\n      'Unexpected `%c`. Expected `[0-9]` for decimal references or `[0-9a-fA-F]` for hexadecimal references'\n  },\n  /** @type {ErrorInfo} */\n  cdataInHtmlContent: {\n    reason: 'Unexpected CDATA section in HTML',\n    description:\n      'Unexpected `<![CDATA[` in HTML. Remove it, use a comment, or encode special characters instead'\n  },\n  /** @type {ErrorInfo} */\n  characterReferenceOutsideUnicodeRange: {\n    reason: 'Unexpected too big numeric character reference',\n    description:\n      'Unexpectedly high character reference. Expected character references to be at most hexadecimal 10ffff (or decimal 1114111)'\n  },\n  /** @type {ErrorInfo} */\n  closingOfElementWithOpenChildElements: {\n    reason: 'Unexpected closing tag with open child elements',\n    description:\n      'Unexpectedly closing tag. Expected other tags to be closed first',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  controlCharacterInInputStream: {\n    reason: 'Unexpected control character',\n    description:\n      'Unexpected control character `%x`. Expected a non-control code point, 0x00, or ASCII whitespace'\n  },\n  /** @type {ErrorInfo} */\n  controlCharacterReference: {\n    reason: 'Unexpected control character reference',\n    description:\n      'Unexpectedly control character in reference. Expected a non-control code point, 0x00, or ASCII whitespace'\n  },\n  /** @type {ErrorInfo} */\n  disallowedContentInNoscriptInHead: {\n    reason: 'Disallowed content inside `<noscript>` in `<head>`',\n    description:\n      'Unexpected text character `%c`. Only use text in `<noscript>`s in `<body>`',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  duplicateAttribute: {\n    reason: 'Unexpected duplicate attribute',\n    description:\n      'Unexpectedly double attribute. Expected attributes to occur only once'\n  },\n  /** @type {ErrorInfo} */\n  endTagWithAttributes: {\n    reason: 'Unexpected attribute on closing tag',\n    description: 'Unexpected attribute. Expected `>` instead'\n  },\n  /** @type {ErrorInfo} */\n  endTagWithTrailingSolidus: {\n    reason: 'Unexpected slash at end of closing tag',\n    description: 'Unexpected `%c-1`. Expected `>` instead'\n  },\n  /** @type {ErrorInfo} */\n  endTagWithoutMatchingOpenElement: {\n    reason: 'Unexpected unopened end tag',\n    description: 'Unexpected end tag. Expected no end tag or another end tag',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  eofBeforeTagName: {\n    reason: 'Unexpected end of file',\n    description: 'Unexpected end of file. Expected tag name instead'\n  },\n  /** @type {ErrorInfo} */\n  eofInCdata: {\n    reason: 'Unexpected end of file in CDATA',\n    description: 'Unexpected end of file. Expected `]]>` to close the CDATA'\n  },\n  /** @type {ErrorInfo} */\n  eofInComment: {\n    reason: 'Unexpected end of file in comment',\n    description: 'Unexpected end of file. Expected `-->` to close the comment'\n  },\n  /** @type {ErrorInfo} */\n  eofInDoctype: {\n    reason: 'Unexpected end of file in doctype',\n    description:\n      'Unexpected end of file. Expected a valid doctype (such as `<!doctype html>`)'\n  },\n  /** @type {ErrorInfo} */\n  eofInElementThatCanContainOnlyText: {\n    reason: 'Unexpected end of file in element that can only contain text',\n    description: 'Unexpected end of file. Expected text or a closing tag',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  eofInScriptHtmlCommentLikeText: {\n    reason: 'Unexpected end of file in comment inside script',\n    description: 'Unexpected end of file. Expected `-->` to close the comment'\n  },\n  /** @type {ErrorInfo} */\n  eofInTag: {\n    reason: 'Unexpected end of file in tag',\n    description: 'Unexpected end of file. Expected `>` to close the tag'\n  },\n  /** @type {ErrorInfo} */\n  incorrectlyClosedComment: {\n    reason: 'Incorrectly closed comment',\n    description: 'Unexpected `%c-1`. Expected `-->` to close the comment'\n  },\n  /** @type {ErrorInfo} */\n  incorrectlyOpenedComment: {\n    reason: 'Incorrectly opened comment',\n    description: 'Unexpected `%c`. Expected `<!--` to open the comment'\n  },\n  /** @type {ErrorInfo} */\n  invalidCharacterSequenceAfterDoctypeName: {\n    reason: 'Invalid sequence after doctype name',\n    description: 'Unexpected sequence at `%c`. Expected `public` or `system`'\n  },\n  /** @type {ErrorInfo} */\n  invalidFirstCharacterOfTagName: {\n    reason: 'Invalid first character in tag name',\n    description: 'Unexpected `%c`. Expected an ASCII letter instead'\n  },\n  /** @type {ErrorInfo} */\n  misplacedDoctype: {\n    reason: 'Misplaced doctype',\n    description: 'Unexpected doctype. Expected doctype before head',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  misplacedStartTagForHeadElement: {\n    reason: 'Misplaced `<head>` start tag',\n    description:\n      'Unexpected start tag `<head>`. Expected `<head>` directly after doctype',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  missingAttributeValue: {\n    reason: 'Missing attribute value',\n    description:\n      'Unexpected `%c-1`. Expected an attribute value or no `%c-1` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingDoctype: {\n    reason: 'Missing doctype before other content',\n    description: 'Expected a `<!doctype html>` before anything else',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  missingDoctypeName: {\n    reason: 'Missing doctype name',\n    description: 'Unexpected doctype end at `%c`. Expected `html` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingDoctypePublicIdentifier: {\n    reason: 'Missing public identifier in doctype',\n    description: 'Unexpected `%c`. Expected identifier for `public` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingDoctypeSystemIdentifier: {\n    reason: 'Missing system identifier in doctype',\n    description:\n      'Unexpected `%c`. Expected identifier for `system` instead (suggested: `\"about:legacy-compat\"`)'\n  },\n  /** @type {ErrorInfo} */\n  missingEndTagName: {\n    reason: 'Missing name in end tag',\n    description: 'Unexpected `%c`. Expected an ASCII letter instead'\n  },\n  /** @type {ErrorInfo} */\n  missingQuoteBeforeDoctypePublicIdentifier: {\n    reason: 'Missing quote before public identifier in doctype',\n    description: 'Unexpected `%c`. Expected `\"` or `\\'` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingQuoteBeforeDoctypeSystemIdentifier: {\n    reason: 'Missing quote before system identifier in doctype',\n    description: 'Unexpected `%c`. Expected `\"` or `\\'` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingSemicolonAfterCharacterReference: {\n    reason: 'Missing semicolon after character reference',\n    description: 'Unexpected `%c`. Expected `;` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingWhitespaceAfterDoctypePublicKeyword: {\n    reason: 'Missing whitespace after public identifier in doctype',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  /** @type {ErrorInfo} */\n  missingWhitespaceAfterDoctypeSystemKeyword: {\n    reason: 'Missing whitespace after system identifier in doctype',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  /** @type {ErrorInfo} */\n  missingWhitespaceBeforeDoctypeName: {\n    reason: 'Missing whitespace before doctype name',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  /** @type {ErrorInfo} */\n  missingWhitespaceBetweenAttributes: {\n    reason: 'Missing whitespace between attributes',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  /** @type {ErrorInfo} */\n  missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers: {\n    reason:\n      'Missing whitespace between public and system identifiers in doctype',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  /** @type {ErrorInfo} */\n  nestedComment: {\n    reason: 'Unexpected nested comment',\n    description: 'Unexpected `<!--`. Expected `-->`'\n  },\n  /** @type {ErrorInfo} */\n  nestedNoscriptInHead: {\n    reason: 'Unexpected nested `<noscript>` in `<head>`',\n    description:\n      'Unexpected `<noscript>`. Expected a closing tag or a meta element',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  nonConformingDoctype: {\n    reason: 'Unexpected non-conforming doctype declaration',\n    description:\n      'Expected `<!doctype html>` or `<!doctype html system \"about:legacy-compat\">`',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  nonVoidHtmlElementStartTagWithTrailingSolidus: {\n    reason: 'Unexpected trailing slash on start tag of non-void element',\n    description: 'Unexpected `/`. Expected `>` instead'\n  },\n  /** @type {ErrorInfo} */\n  noncharacterCharacterReference: {\n    reason:\n      'Unexpected noncharacter code point referenced by character reference',\n    description: 'Unexpected code point. Do not use noncharacters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  noncharacterInInputStream: {\n    reason: 'Unexpected noncharacter character',\n    description: 'Unexpected code point `%x`. Do not use noncharacters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  nullCharacterReference: {\n    reason: 'Unexpected NULL character referenced by character reference',\n    description: 'Unexpected code point. Do not use NULL characters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  openElementsLeftAfterEof: {\n    reason: 'Unexpected end of file',\n    description: 'Unexpected end of file. Expected closing tag instead',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  surrogateCharacterReference: {\n    reason: 'Unexpected surrogate character referenced by character reference',\n    description:\n      'Unexpected code point. Do not use lone surrogate characters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  surrogateInInputStream: {\n    reason: 'Unexpected surrogate character',\n    description:\n      'Unexpected code point `%x`. Do not use lone surrogate characters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedCharacterAfterDoctypeSystemIdentifier: {\n    reason: 'Invalid character after system identifier in doctype',\n    description: 'Unexpected character at `%c`. Expected `>`'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedCharacterInAttributeName: {\n    reason: 'Unexpected character in attribute name',\n    description:\n      'Unexpected `%c`. Expected whitespace, `/`, `>`, `=`, or probably an ASCII letter'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedCharacterInUnquotedAttributeValue: {\n    reason: 'Unexpected character in unquoted attribute value',\n    description: 'Unexpected `%c`. Quote the attribute value to include it'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedEqualsSignBeforeAttributeName: {\n    reason: 'Unexpected equals sign before attribute name',\n    description: 'Unexpected `%c`. Add an attribute name before it'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedNullCharacter: {\n    reason: 'Unexpected NULL character',\n    description:\n      'Unexpected code point `%x`. Do not use NULL characters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedQuestionMarkInsteadOfTagName: {\n    reason: 'Unexpected question mark instead of tag name',\n    description: 'Unexpected `%c`. Expected an ASCII letter instead'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedSolidusInTag: {\n    reason: 'Unexpected slash in tag',\n    description:\n      'Unexpected `%c-1`. Expected it followed by `>` or in a quoted attribute value'\n  },\n  /** @type {ErrorInfo} */\n  unknownNamedCharacterReference: {\n    reason: 'Unexpected unknown named character reference',\n    description:\n      'Unexpected character reference. Expected known named character references'\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-from-html/lib/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-from-html/lib/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/hast-util-from-html/lib/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromHtml: () => (/* binding */ fromHtml)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var hast_util_from_parse5__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-from-parse5 */ \"(ssr)/./node_modules/hast-util-from-parse5/lib/index.js\");\n/* harmony import */ var parse5__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! parse5 */ \"(ssr)/./node_modules/parse5/dist/index.js\");\n/* harmony import */ var vfile__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! vfile */ \"(ssr)/./node_modules/vfile/lib/index.js\");\n/* harmony import */ var vfile_message__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! vfile-message */ \"(ssr)/./node_modules/vfile-message/lib/index.js\");\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./errors.js */ \"(ssr)/./node_modules/hast-util-from-html/lib/errors.js\");\n/**\n * @import {Root} from 'hast'\n * @import {ParserError} from 'parse5'\n * @import {Value} from 'vfile'\n * @import {ErrorCode, Options} from './types.js'\n */\n\n\n\n\n\n\n\n\nconst base = 'https://html.spec.whatwg.org/multipage/parsing.html#parse-error-'\n\nconst dashToCamelRe = /-[a-z]/g\nconst formatCRe = /%c(?:([-+])(\\d+))?/g\nconst formatXRe = /%x/g\n\nconst fatalities = {2: true, 1: false, 0: null}\n\n/** @type {Readonly<Options>} */\nconst emptyOptions = {}\n\n/**\n * Turn serialized HTML into a hast tree.\n *\n * @param {VFile | Value} value\n *   Serialized HTML to parse.\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {Root}\n *   Tree.\n */\nfunction fromHtml(value, options) {\n  const settings = options || emptyOptions\n  const onerror = settings.onerror\n  const file = value instanceof vfile__WEBPACK_IMPORTED_MODULE_1__.VFile ? value : new vfile__WEBPACK_IMPORTED_MODULE_1__.VFile(value)\n  const parseFunction = settings.fragment ? parse5__WEBPACK_IMPORTED_MODULE_0__.parseFragment : parse5__WEBPACK_IMPORTED_MODULE_0__.parse\n  const document = String(file)\n  const p5Document = parseFunction(document, {\n    sourceCodeLocationInfo: true,\n    // Note `parse5` types currently do not allow `undefined`.\n    onParseError: settings.onerror ? internalOnerror : null,\n    scriptingEnabled: false\n  })\n\n  // `parse5` returns document which are always mapped to roots.\n  return /** @type {Root} */ (\n    (0,hast_util_from_parse5__WEBPACK_IMPORTED_MODULE_2__.fromParse5)(p5Document, {\n      file,\n      space: settings.space,\n      verbose: settings.verbose\n    })\n  )\n\n  /**\n   * Handle a parse error.\n   *\n   * @param {ParserError} error\n   *   Parse5 error.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function internalOnerror(error) {\n    const code = error.code\n    const name = camelcase(code)\n    const setting = settings[name]\n    const config = setting === null || setting === undefined ? true : setting\n    const level = typeof config === 'number' ? config : config ? 1 : 0\n\n    if (level) {\n      const info = _errors_js__WEBPACK_IMPORTED_MODULE_3__.errors[name]\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(info, 'expected known error from `parse5`')\n\n      const message = new vfile_message__WEBPACK_IMPORTED_MODULE_5__.VFileMessage(format(info.reason), {\n        place: {\n          start: {\n            line: error.startLine,\n            column: error.startCol,\n            offset: error.startOffset\n          },\n          end: {\n            line: error.endLine,\n            column: error.endCol,\n            offset: error.endOffset\n          }\n        },\n        ruleId: code,\n        source: 'hast-util-from-html'\n      })\n\n      if (file.path) {\n        message.file = file.path\n        message.name = file.path + ':' + message.name\n      }\n\n      message.fatal = fatalities[level]\n      message.note = format(info.description)\n      message.url = info.url === false ? undefined : base + code\n\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(onerror, '`internalOnerror` is not passed if `onerror` is not set')\n      onerror(message)\n    }\n\n    /**\n     * Format a human readable string about an error.\n     *\n     * @param {string} value\n     *   Value to format.\n     * @returns {string}\n     *   Formatted.\n     */\n    function format(value) {\n      return value.replace(formatCRe, formatC).replace(formatXRe, formatX)\n\n      /**\n       * Format the character.\n       *\n       * @param {string} _\n       *   Match.\n       * @param {string} $1\n       *   Sign (`-` or `+`, optional).\n       * @param {string} $2\n       *   Offset.\n       * @returns {string}\n       *   Formatted.\n       */\n      function formatC(_, $1, $2) {\n        const offset =\n          ($2 ? Number.parseInt($2, 10) : 0) * ($1 === '-' ? -1 : 1)\n        const char = document.charAt(error.startOffset + offset)\n        return visualizeCharacter(char)\n      }\n\n      /**\n       * Format the character code.\n       *\n       * @returns {string}\n       *   Formatted.\n       */\n      function formatX() {\n        return visualizeCharacterCode(document.charCodeAt(error.startOffset))\n      }\n    }\n  }\n}\n\n/**\n * @param {string} value\n *   Error code in dash case.\n * @returns {ErrorCode}\n *   Error code in camelcase.\n */\nfunction camelcase(value) {\n  // This should match an error code.\n  return /** @type {ErrorCode} */ (value.replace(dashToCamelRe, dashToCamel))\n}\n\n/**\n * @param {string} $0\n *   Match.\n * @returns {string}\n *   Camelcased.\n */\nfunction dashToCamel($0) {\n  return $0.charAt(1).toUpperCase()\n}\n\n/**\n * @param {string} char\n *   Character.\n * @returns {string}\n *   Formatted.\n */\nfunction visualizeCharacter(char) {\n  return char === '`' ? '` ` `' : char\n}\n\n/**\n * @param {number} charCode\n *   Character code.\n * @returns {string}\n *   Formatted.\n */\nfunction visualizeCharacterCode(charCode) {\n  return '0x' + charCode.toString(16).toUpperCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-from-html/lib/index.js\n");

/***/ })

};
;