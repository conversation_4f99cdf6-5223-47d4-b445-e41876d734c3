/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/start-lesson/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cstart-lesson%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cstart-lesson%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/start-lesson/page.tsx */ \"(app-pages-browser)/./src/app/start-lesson/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDcGMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNEZXNrdG9wJTVDJTVDU29seW50YV9XZWJzaXRlJTVDJTVDZnJvbnRlbmQlNUMlNUNsZXNzb24tcGxhdGZvcm0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNzdGFydC1sZXNzb24lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLHdMQUE0SiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccGNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEZXNrdG9wXFxcXFNvbHludGFfV2Vic2l0ZVxcXFxmcm9udGVuZFxcXFxsZXNzb24tcGxhdGZvcm1cXFxcc3JjXFxcXGFwcFxcXFxzdGFydC1sZXNzb25cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cstart-lesson%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/start-lesson/page.tsx":
/*!***************************************!*\
  !*** ./src/app/start-lesson/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StartLessonPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(app-pages-browser)/./src/components/ui/LoadingSpinner.tsx\");\n/* harmony import */ var _components_shadcn_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/shadcn/button */ \"(app-pages-browser)/./src/components/shadcn/button.tsx\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n // Import useEffect\n\n\n\n// --- Corrected Firebase import path ---\n\n// -------------------------------------\n // Import onAuthStateChanged\nfunction StartLessonPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Extract all required lesson/session params from query string\n    const studentId = searchParams.get(\"studentId\") || searchParams.get(\"student_id\") || \"\";\n    const lessonRef = searchParams.get(\"lessonRef\") || \"\";\n    const country = searchParams.get(\"country\") || \"\";\n    const curriculum = searchParams.get(\"curriculum\") || \"\";\n    const grade = searchParams.get(\"grade\") || \"\";\n    const level = searchParams.get(\"level\") || \"\";\n    const subject = searchParams.get(\"subject\") || \"\";\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAuthReady, setIsAuthReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // State to track auth readiness\n    // Wait for Firebase Auth to initialize\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StartLessonPage.useEffect\": ()=>{\n            const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_6__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_5__.auth, {\n                \"StartLessonPage.useEffect.unsubscribe\": (user)=>{\n                    setIsAuthReady(true); // Mark auth as ready once the state is known\n                    if (!user) {\n                        console.warn(\"[StartLessonPage] Firebase user not logged in on mount.\");\n                    // Optionally redirect immediately, or let handleStartLesson handle it\n                    // router.push('/login'); \n                    } else {\n                        console.log(\"[StartLessonPage] Firebase user detected:\", user.uid);\n                    }\n                }\n            }[\"StartLessonPage.useEffect.unsubscribe\"]);\n            return ({\n                \"StartLessonPage.useEffect\": ()=>unsubscribe()\n            })[\"StartLessonPage.useEffect\"]; // Cleanup subscription on unmount\n        }\n    }[\"StartLessonPage.useEffect\"], [\n        router\n    ]);\n    // Helper for exponential backoff retry\n    async function fetchWithRetry(url, options) {\n        let retries = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 3, delay = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 500;\n        try {\n            const response = await fetch(url, options);\n            if (!response.ok) {\n                // Log more details on failure\n                let errorBody = \"Server responded with \".concat(response.status);\n                try {\n                    const bodyText = await response.text();\n                    errorBody += \": \".concat(bodyText);\n                } catch (e) {}\n                console.error(\"Fetch failed with status \".concat(response.status, \": \").concat(errorBody));\n                throw new Error(errorBody);\n            }\n            return response;\n        } catch (error) {\n            console.error(\"fetchWithRetry error (retries left: \".concat(retries, \"):\"), error.message);\n            if (retries > 0) {\n                await new Promise((res)=>setTimeout(res, delay));\n                return fetchWithRetry(url, options, retries - 1, delay * 2);\n            }\n            throw error; // Re-throw the final error\n        }\n    }\n    async function handleStartLesson() {\n        if (!isAuthReady) {\n            setError(\"Authentication check is still in progress. Please wait a moment.\");\n            return;\n        }\n        if (!studentId || !lessonRef || !subject || !grade || !country || !curriculum) {\n            const missing = [\n                !studentId ? 'studentId' : null,\n                !lessonRef ? 'lessonRef' : null,\n                !country ? 'country' : null,\n                !curriculum ? 'curriculum' : null,\n                !grade ? 'grade' : null,\n                !subject ? 'subject' : null\n            ].filter(Boolean).join(', ');\n            setError(\"Missing required parameters: \".concat(missing));\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        let idToken = null;\n        try {\n            const user = _lib_firebase__WEBPACK_IMPORTED_MODULE_5__.auth.currentUser;\n            if (!user) {\n                throw new Error(\"No authenticated user found. Please log in.\");\n            }\n            // --- Get fresh ID token ---\n            try {\n                console.log(\"[StartLessonPage] Attempting to get ID token (force refresh)...\");\n                idToken = await user.getIdToken(true); // forceRefresh = true\n                if (!idToken) {\n                    throw new Error(\"Failed to obtain a valid ID token (getIdToken returned null/undefined).\");\n                }\n                console.log(\"[StartLessonPage] Successfully obtained ID token.\");\n            } catch (tokenError) {\n                console.error(\"[StartLessonPage] Error getting ID token:\", tokenError);\n                throw new Error(\"Failed to get authentication token: \".concat(tokenError.message, \". Please try logging out and back in.\"));\n            }\n            // ------------------------\n            console.log(\"[StartLessonPage] ID Token obtained (first 20 chars):\", idToken ? idToken.substring(0, 20) + '...' : 'null/undefined');\n            // Call /api/lesson-content to initialize the session\n            console.log(\"[StartLessonPage] Calling session initialization API (/api/lesson-content)...\");\n            const sessionInitResponse = await fetchWithRetry(\"/api/lesson-content\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(idToken)\n                },\n                body: JSON.stringify({\n                    student_id: studentId,\n                    lessonRef: lessonRef,\n                    subject: subject,\n                    country: country,\n                    curriculum: curriculum,\n                    grade: grade,\n                    current_phase: \"diagnostic_start_probe\",\n                    student_name: localStorage.getItem('student_name') || undefined\n                })\n            });\n            console.log(\"[StartLessonPage] /api/lesson-content response status:\", sessionInitResponse.status);\n            let sessionInitData;\n            try {\n                sessionInitData = await sessionInitResponse.json();\n                // VERY IMPORTANT LOG:\n                console.log(\"[StartLessonPage] RAW response from /lesson-content proxy:\", JSON.stringify(sessionInitData, null, 2));\n            } catch (jsonError) {\n                console.error(\"[StartLessonPage] Failed to parse JSON response from /lesson-content proxy:\", jsonError.message);\n                const responseText = await sessionInitResponse.text().catch(()=>\"Could not read response text.\");\n                console.error(\"[StartLessonPage] Raw response text from proxy was:\", responseText);\n                throw new Error(\"Received invalid JSON response from proxy. Response text: \".concat(responseText.substring(0, 200)));\n            }\n            // Log individual parts\n            console.log(\"[StartLessonPage] sessionInitData.success:\", sessionInitData.success);\n            console.log(\"[StartLessonPage] sessionInitData.message:\", sessionInitData.message);\n            console.log(\"[StartLessonPage] sessionInitData.sessionId (top-level):\", sessionInitData.sessionId);\n            console.log(\"[StartLessonPage] sessionInitData.data:\", sessionInitData.data);\n            if (sessionInitData.data) {\n                console.log(\"[StartLessonPage] sessionInitData.data.session_id (nested):\", sessionInitData.data.session_id);\n            }\n            const successFlag = sessionInitData.success;\n            const messageFromServer = sessionInitData.message || \"Unknown server message.\";\n            const extractedSessionId = sessionInitData.sessionId || sessionInitData.data && sessionInitData.data.session_id || null;\n            console.log(\"[StartLessonPage] Evaluation: successFlag=\".concat(successFlag, \", messageFromServer='\").concat(messageFromServer, \"', extractedSessionId='\").concat(extractedSessionId, \"'\"));\n            if (!successFlag || !extractedSessionId) {\n                const errorMessage = \"API call was logically successful (message: '\".concat(messageFromServer, \"') but session ID was missing or invalid (extracted: '\").concat(extractedSessionId, \"').\");\n                console.error(\"[StartLessonPage] Critical logic failure:\", errorMessage, \"Full Proxy Response:\", sessionInitData);\n                // Throw a more specific error to distinguish from actual API call failures\n                throw new Error(\"LogicError: \".concat(errorMessage));\n            }\n            const newSessionId = extractedSessionId;\n            console.log(\"[StartLessonPage] Obtained session ID:\", newSessionId);\n            // On success, redirect to classroom page with necessary params\n            const redirectParams = new URLSearchParams({\n                session_id: newSessionId,\n                lessonRef: lessonRef,\n                studentId: studentId,\n                subject: subject,\n                country: country,\n                curriculum: curriculum,\n                grade: grade\n            });\n            const redirectUrl = \"/classroom?\".concat(redirectParams.toString());\n            console.log(\"[StartLessonPage] Redirecting to:\", redirectUrl);\n            router.push(redirectUrl);\n        } catch (err) {\n            console.error(\"[StartLessonPage] Error in handleStartLesson:\", err);\n            // Provide more context in error messages\n            let displayError = err.message || \"An unexpected error occurred while starting the lesson.\";\n            if (displayError.includes(\"status 401\") || displayError.includes(\"authentication\")) {\n                displayError += \" Please try logging out and back in.\";\n            } else if (displayError.includes(\"status 404\")) {\n                displayError = \"Could not find the requested lesson (\".concat(lessonRef, \"). Please check the lesson details.\");\n            } else if (displayError.includes(\"status 500\")) {\n                displayError = \"A server error occurred. Please try again later or contact support.\";\n            }\n            setError(displayError);\n        } finally{\n            setLoading(false);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center p-4 bg-gray-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white shadow-lg rounded-lg p-8 w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-6 text-center text-gray-800\",\n                    children: \"Start Your Lesson\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 space-y-1 text-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                    children: \"Student ID:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 14\n                                }, this),\n                                \" \",\n                                studentId || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-500\",\n                                    children: \"Missing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 47\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                    children: \"Lesson Ref:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 14\n                                }, this),\n                                \" \",\n                                lessonRef || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-500\",\n                                    children: \"Missing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 47\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                    children: \"Subject:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 14\n                                }, this),\n                                \" \",\n                                subject || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-500\",\n                                    children: \"Missing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 42\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                    children: \"Grade:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 14\n                                }, this),\n                                \" \",\n                                grade || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-500\",\n                                    children: \"Missing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 38\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                    children: \"Curriculum:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 14\n                                }, this),\n                                \" \",\n                                curriculum || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-500\",\n                                    children: \"Missing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 48\n                                }, this),\n                                \" (\",\n                                country || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-500\",\n                                    children: \"Missing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 108\n                                }, this),\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4\",\n                    role: \"alert\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            className: \"font-bold\",\n                            children: \"Error: \"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 17\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"block sm:inline\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 17\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 13\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150 ease-in-out disabled:opacity-50\",\n                    onClick: handleStartLesson,\n                    disabled: loading || !isAuthReady || !studentId || !lessonRef || !subject || !grade || !country || !curriculum,\n                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 22\n                    }, this) : isAuthReady ? \"Start Lesson\" : \"Initializing...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this),\n                !isAuthReady && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-center text-gray-500 mt-2\",\n                    children: \"Waiting for authentication...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 27\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n            lineNumber: 213,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\start-lesson\\\\page.tsx\",\n        lineNumber: 212,\n        columnNumber: 5\n    }, this);\n}\n_s(StartLessonPage, \"HTuyRKCVG4NBUG718W7dZ3B9+Ls=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = StartLessonPage;\nvar _c;\n$RefreshReg$(_c, \"StartLessonPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/start-lesson/page.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["firebase","vendors","common","main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cstart-lesson%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);