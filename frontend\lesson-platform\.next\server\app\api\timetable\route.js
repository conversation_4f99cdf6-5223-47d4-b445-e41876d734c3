/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/timetable/route";
exports.ids = ["app/api/timetable/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftimetable%2Froute&page=%2Fapi%2Ftimetable%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftimetable%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftimetable%2Froute&page=%2Fapi%2Ftimetable%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftimetable%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_timetable_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/timetable/route.ts */ \"(rsc)/./src/app/api/timetable/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/timetable/route\",\n        pathname: \"/api/timetable\",\n        filename: \"route\",\n        bundlePath: \"app/api/timetable/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\api\\\\timetable\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_timetable_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftimetable%2Froute&page=%2Fapi%2Ftimetable%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftimetable%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/timetable/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/timetable/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var firebase_admin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase-admin */ \"firebase-admin\");\n/* harmony import */ var firebase_admin__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(firebase_admin__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(rsc)/./node_modules/firebase/auth/dist/index.mjs\");\n\n\n\n// Firebase Admin singleton\nlet firebaseApp = null;\nlet firestoreDb = null;\n// Initialize Firebase Admin with proper singleton pattern\nfunction initializeFirebaseAdmin() {\n    // Return existing app if already initialized\n    if (firebaseApp) {\n        console.log('[Timetable API] Using existing Firebase Admin app');\n        return firebaseApp;\n    }\n    try {\n        console.log('[Timetable API] Initializing Firebase Admin...');\n        // Check if default app already exists\n        try {\n            const existingApp = firebase_admin__WEBPACK_IMPORTED_MODULE_1___default().app();\n            console.log('[Timetable API] Using existing default Firebase Admin app');\n            firebaseApp = existingApp;\n            return firebaseApp;\n        } catch (error) {\n        // Default app doesn't exist, create new one\n        }\n        // Validate environment variables first\n        const requiredVars = [\n            'FIREBASE_PROJECT_ID',\n            'FIREBASE_CLIENT_EMAIL',\n            'FIREBASE_PRIVATE_KEY_ID',\n            'FIREBASE_CLIENT_ID',\n            'FIREBASE_PRIVATE_KEY'\n        ];\n        const missingVars = requiredVars.filter((varName)=>!process.env[varName]);\n        if (missingVars.length > 0) {\n            console.error('[Timetable API] Missing environment variables:', missingVars);\n            throw new Error(`Missing environment variables: ${missingVars.join(', ')}`);\n        }\n        // Process the private key to handle different newline formats\n        let privateKey = process.env.FIREBASE_PRIVATE_KEY;\n        if (privateKey) {\n            // Remove surrounding quotes if they exist\n            if (privateKey.startsWith('\"') && privateKey.endsWith('\"')) {\n                privateKey = privateKey.slice(1, -1);\n            }\n            // Handle different possible newline encodings\n            privateKey = privateKey.replace(/\\\\\\\\n/g, '\\n') // Handle double-escaped newlines\n            .replace(/\\\\n/g, '\\n'); // Handle escaped newlines\n            console.log('[Timetable API] Processed private key length:', privateKey.length);\n        }\n        const serviceAccount = {\n            \"type\": \"service_account\",\n            \"project_id\": process.env.FIREBASE_PROJECT_ID,\n            \"private_key_id\": process.env.FIREBASE_PRIVATE_KEY_ID,\n            \"private_key\": privateKey,\n            \"client_email\": process.env.FIREBASE_CLIENT_EMAIL,\n            \"client_id\": process.env.FIREBASE_CLIENT_ID,\n            \"auth_uri\": \"https://accounts.google.com/o/oauth2/auth\",\n            \"token_uri\": \"https://oauth2.googleapis.com/token\",\n            \"auth_provider_x509_cert_url\": \"https://www.googleapis.com/oauth2/v1/certs\",\n            \"client_x509_cert_url\": `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.FIREBASE_CLIENT_EMAIL?.replace('@', '%40')}`,\n            \"universe_domain\": \"googleapis.com\"\n        };\n        // Initialize Firebase Admin with default app name (no cleanup needed)\n        firebaseApp = firebase_admin__WEBPACK_IMPORTED_MODULE_1___default().initializeApp({\n            credential: firebase_admin__WEBPACK_IMPORTED_MODULE_1___default().credential.cert(serviceAccount),\n            projectId: serviceAccount.project_id\n        });\n        console.log('[Timetable API] Firebase Admin initialized successfully');\n        return firebaseApp;\n    } catch (error) {\n        console.error('[Timetable API] Firebase initialization error:', error);\n        throw error;\n    }\n}\n// Get Firebase Admin database instance\nfunction getFirebaseDb() {\n    // Use cached Firestore instance if available\n    if (firestoreDb) {\n        return firestoreDb;\n    }\n    // Create new Firestore instance and cache it\n    const app = initializeFirebaseAdmin();\n    firestoreDb = app.firestore();\n    console.log('[Timetable API] Firestore instance created and cached');\n    return firestoreDb;\n}\n// Define the standard time slots for timetable generation\nconst STANDARD_TIME_SLOTS = [\n    '08:00-08:45',\n    '08:50-09:35',\n    '09:40-10:25',\n    '10:40-11:25',\n    '11:30-12:15',\n    '13:15-14:00',\n    '14:15-15:00'\n];\nconst DAYS_OF_WEEK = [\n    'Monday',\n    'Tuesday',\n    'Wednesday',\n    'Thursday',\n    'Friday'\n];\n// Subject cognitive load mapping (1-10, where 10 is highest cognitive load)\nconst SUBJECT_COGNITIVE_LOADS = {\n    'mathematics': 9,\n    'english_language': 8,\n    'basic_science_and_technology': 7,\n    'basic_science': 7,\n    'social_studies': 6,\n    'computer_studies': 8,\n    'computing': 8,\n    'artificial_intelligence': 9,\n    'french': 7,\n    'christian_religious_knowledge': 5,\n    'national_values_education': 4,\n    'entrepreneurship_education': 5,\n    'entrepreneurship': 5,\n    'financial_literacy': 6,\n    'creative_arts': 3,\n    'cultural_and_creative_arts': 3,\n    'art_and_design': 3,\n    'physical_health_education': 2,\n    'project_based_excellence': 6\n};\n// Get the curriculum path for lesson content lookup\nfunction getCurriculumPath(studentData, subjectName) {\n    // Convert grade format for curriculum path\n    const gradeForPath = studentData.grade.replace('-', ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase());\n    // Normalize subject name for curriculum path\n    const normalizedSubjectName = subjectName.replace(/\\b\\w/g, (l)=>l.toUpperCase());\n    return `/countries/${studentData.country}/curriculums/${studentData.curriculum}/grades/${gradeForPath}/levels/${studentData.level}/subjects/${normalizedSubjectName}`;\n}\n// Calculate lesson reference for a specific week and subject\nfunction generateLessonReference(grade, subjectCode, academicWeek, lessonNumber, lessonsPerWeek) {\n    // Calculate the absolute lesson number across all weeks\n    const absoluteLessonNumber = (academicWeek - 1) * lessonsPerWeek + lessonNumber;\n    // Format lesson number with leading zeros (e.g., 001, 002, etc.)\n    const formattedLessonNumber = absoluteLessonNumber.toString().padStart(3, '0');\n    // Convert grade to proper abbreviated format\n    const abbreviatedGrade = convertGradeToAbbreviation(grade);\n    return `${abbreviatedGrade}-${subjectCode}-${formattedLessonNumber}`;\n}\n// Convert grade level to proper abbreviated format\nfunction convertGradeToAbbreviation(grade) {\n    // Handle different grade formats\n    const gradeStr = grade.toLowerCase().trim();\n    // Nursery grades\n    if (gradeStr.includes('pre-nursery') || gradeStr.includes('prenursery') || gradeStr === 'pre nursery') {\n        return 'NUR0';\n    }\n    if (gradeStr.includes('nursery')) {\n        const match = gradeStr.match(/(\\d+)/);\n        if (match) {\n            return `NUR${match[1]}`;\n        }\n        // Default to NUR1 if no number found\n        return 'NUR1';\n    }\n    // Primary grades\n    if (gradeStr.includes('primary') || gradeStr.startsWith('p')) {\n        const match = gradeStr.match(/(\\d+)/);\n        if (match) {\n            return `P${match[1]}`;\n        }\n    }\n    // Junior Secondary\n    if (gradeStr.includes('junior') || gradeStr.includes('jss')) {\n        const match = gradeStr.match(/(\\d+)/);\n        if (match) {\n            return `JSS${match[1]}`;\n        }\n    }\n    // Senior Secondary\n    if (gradeStr.includes('senior') || gradeStr.includes('sss')) {\n        const match = gradeStr.match(/(\\d+)/);\n        if (match) {\n            return `SSS${match[1]}`;\n        }\n    }\n    // If already in correct format, return as is\n    if (/^(NUR\\d+|P|JSS|SSS)\\d+$/i.test(grade)) {\n        return grade.toUpperCase();\n    }\n    // Default fallback\n    return grade;\n}\n// Fetch student enrollments from Firestore\nasync function fetchStudentEnrollments(studentId) {\n    try {\n        console.log(`[Timetable API] Fetching enrollments for student: ${studentId}`);\n        // Try different student ID formats\n        const studentIdFormats = [\n            studentId,\n            `andrea_ugono_33305`,\n            studentId.toLowerCase(),\n            studentId.replace(/\\s+/g, '_').toLowerCase()\n        ];\n        let enrollments = [];\n        let studentRef = null;\n        for (const format of studentIdFormats){\n            console.log(`[Timetable API] Trying student ID format: ${format}`);\n            const db = getFirebaseDb();\n            const testStudentRef = db.collection('students').doc(format);\n            const testStudentSnap = await testStudentRef.get();\n            if (testStudentSnap.exists) {\n                console.log(`[Timetable API] Found student document with ID: ${format}`);\n                studentRef = testStudentRef;\n                break;\n            }\n        }\n        if (!studentRef) {\n            console.warn(`[Timetable API] No student document found for any ID format`);\n            return [];\n        }\n        // Fetch enrollments from the enrollments subcollection\n        const enrollmentsRef = studentRef.collection('enrollments');\n        const enrollmentsSnap = await enrollmentsRef.where('status', '==', 'active').get();\n        console.log(`[Timetable API] Found ${enrollmentsSnap.size} active enrollments`);\n        enrollmentsSnap.docs.forEach((doc)=>{\n            const data = doc.data();\n            const subjectId = doc.id;\n            enrollments.push({\n                subjectId: subjectId,\n                subjectName: data.subjectName || data.subject_name || doc.id.replace(/_/g, ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n                subjectCode: data.subjectCode || data.subject_code || getSubjectCodeFromId(doc.id),\n                lessonsPerWeek: data.lessonsPerWeek || data.lessons_per_week || 1,\n                status: data.status,\n                enrolledAt: data.enrolledAt || data.enrolled_at,\n                cognitiveLoad: data.cognitiveLoad || SUBJECT_COGNITIVE_LOADS[subjectId] || 5 // Default to medium cognitive load\n            });\n        });\n        console.log(`[Timetable API] Processed enrollments:`, enrollments);\n        return enrollments;\n    } catch (error) {\n        console.error(`[Timetable API] Error fetching enrollments:`, error);\n        return [];\n    }\n}\n// Get subject code from subject ID\nfunction getSubjectCodeFromId(subjectId) {\n    // Normalize the input: convert to lowercase and replace hyphens/spaces with underscores\n    const normalizedId = subjectId.toLowerCase().replace(/[-\\s]+/g, '_').replace(/[^a-z0-9_]/g, ''); // Remove any other special characters\n    const codeMap = {\n        // Nursery subject codes\n        'basic_digital_literacy': 'BDL',\n        'language_and_communication': 'LC',\n        'numeracy': 'NUM',\n        'cognitive_development': 'CD',\n        'social_emotional_development': 'SED',\n        // Primary and secondary subject codes\n        'mathematics': 'MAT',\n        'english_language': 'ENG',\n        'basic_science_and_technology': 'BST',\n        'basic_science': 'BST',\n        'social_studies': 'SST',\n        'computer_studies': 'COM',\n        'computing': 'COM',\n        'creative_arts': 'ART',\n        'cultural_and_creative_arts': 'CCA',\n        'art_and_design': 'ART',\n        'physical_health_education': 'PHE',\n        'national_values_education': 'NVE',\n        'entrepreneurship_education': 'ENT',\n        'entrepreneurship': 'ENT',\n        'financial_literacy': 'FIL',\n        'french': 'FRE',\n        'artificial_intelligence': 'AI',\n        'project_based_excellence': 'PBE',\n        'christian_religious_knowledge': 'CRK'\n    };\n    // Try the normalized ID first\n    if (codeMap[normalizedId]) {\n        return codeMap[normalizedId];\n    }\n    // Try the original ID as fallback\n    if (codeMap[subjectId.toLowerCase()]) {\n        return codeMap[subjectId.toLowerCase()];\n    }\n    // Special handling for project-based excellence variants\n    if (subjectId.toLowerCase().includes('project') && subjectId.toLowerCase().includes('excellence')) {\n        return 'PBE';\n    }\n    return 'GEN';\n}\n// Distribute lessons across the week based on lessons per week and cognitive load\nfunction distributeLessonsAcrossWeek(enrollments, academicWeek, studentData) {\n    const weeklyLessons = [];\n    console.log(`[Timetable API] Distributing lessons for ${enrollments.length} subjects across the week`);\n    // Create a weekly schedule grid: [day][timeSlot]\n    const weekSchedule = DAYS_OF_WEEK.map(()=>new Array(STANDARD_TIME_SLOTS.length).fill(null));\n    // Calculate total lessons needed\n    const totalLessonsNeeded = enrollments.reduce((sum, e)=>sum + e.lessonsPerWeek, 0);\n    const totalSlotsAvailable = DAYS_OF_WEEK.length * STANDARD_TIME_SLOTS.length; // 5 days × 7 slots = 35\n    console.log(`[Timetable API] Total lessons needed: ${totalLessonsNeeded}, Total slots available: ${totalSlotsAvailable}`);\n    // Sort enrollments by cognitive load (highest first) for optimal time slot assignment\n    const sortedEnrollments = [\n        ...enrollments\n    ].sort((a, b)=>(b.cognitiveLoad || 5) - (a.cognitiveLoad || 5));\n    console.log(`[Timetable API] Sorted subjects by cognitive load:`, sortedEnrollments.map((e)=>`${e.subjectName} (${e.cognitiveLoad}) - ${e.lessonsPerWeek} lessons`));\n    // Track how many lessons each subject has been assigned\n    const subjectLessonCounts = {};\n    // Initialize all subjects\n    sortedEnrollments.forEach((enrollment)=>{\n        subjectLessonCounts[enrollment.subjectId] = 0;\n    });\n    // Schedule all lessons using a more aggressive approach\n    scheduleAllLessonsOptimally(sortedEnrollments, weekSchedule, academicWeek, studentData, subjectLessonCounts);\n    // Fill any remaining empty slots with \"Free Period\"\n    fillEmptySlotsWithFreePeriods(weekSchedule, academicWeek, studentData);\n    // Convert the schedule grid back to lesson objects\n    for(let dayIndex = 0; dayIndex < DAYS_OF_WEEK.length; dayIndex++){\n        for(let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++){\n            const lesson = weekSchedule[dayIndex][timeIndex];\n            if (lesson) {\n                weeklyLessons.push(lesson);\n            }\n        }\n    }\n    console.log(`[Timetable API] Generated ${weeklyLessons.length} lessons for the week (target: ${totalSlotsAvailable})`);\n    // Log final subject distribution\n    const finalCounts = {};\n    weeklyLessons.forEach((lesson)=>{\n        if (lesson.subjectId && lesson.subjectId !== 'free_period') {\n            finalCounts[lesson.subjectId] = (finalCounts[lesson.subjectId] || 0) + 1;\n        }\n    });\n    console.log(`[Timetable API] Final lesson distribution:`, finalCounts);\n    return weeklyLessons;\n}\n// New optimized scheduling function that ensures all lessons are scheduled\nfunction scheduleAllLessonsOptimally(enrollments, weekSchedule, academicWeek, studentData, subjectLessonCounts) {\n    // Create a list of all lessons that need to be scheduled (without lesson numbers initially)\n    const lessonsToSchedule = [];\n    enrollments.forEach((enrollment)=>{\n        for(let i = 1; i <= enrollment.lessonsPerWeek; i++){\n            lessonsToSchedule.push({\n                enrollment\n            });\n        }\n    });\n    console.log(`[Timetable API] Total lessons to schedule: ${lessonsToSchedule.length}`);\n    // Sort lessons by cognitive load (highest first)\n    lessonsToSchedule.sort((a, b)=>{\n        return (b.enrollment.cognitiveLoad || 5) - (a.enrollment.cognitiveLoad || 5);\n    });\n    // Track lessons per day for each subject to enforce distribution rules\n    const subjectDailyCount = {};\n    enrollments.forEach((enrollment)=>{\n        subjectDailyCount[enrollment.subjectId] = new Array(DAYS_OF_WEEK.length).fill(0);\n    });\n    // Schedule each lesson (without assigning lesson numbers yet)\n    for (const lessonToSchedule of lessonsToSchedule){\n        const { enrollment } = lessonToSchedule;\n        let scheduled = false;\n        // Get preferred time slots based on cognitive load\n        const preferredTimeSlots = getPreferredTimeSlots(enrollment.cognitiveLoad || 5);\n        // Try to schedule on each day, prioritizing days with fewer lessons for this subject\n        const dayPriority = Array.from({\n            length: DAYS_OF_WEEK.length\n        }, (_, i)=>i).sort((a, b)=>subjectDailyCount[enrollment.subjectId][a] - subjectDailyCount[enrollment.subjectId][b]);\n        for (const dayIndex of dayPriority){\n            // Special rule for Mathematics: allow up to 2 lessons on Monday, 1 on other days\n            const maxLessonsPerDay = enrollment.subjectId === 'mathematics' && dayIndex === 0 ? 2 : enrollment.lessonsPerWeek >= 6 ? 2 : 1;\n            if (subjectDailyCount[enrollment.subjectId][dayIndex] >= maxLessonsPerDay) {\n                continue; // Skip this day if we've reached the limit\n            }\n            // Try preferred time slots first\n            for (const timeIndex of preferredTimeSlots){\n                if (weekSchedule[dayIndex][timeIndex] === null) {\n                    // Check if this would create back-to-back lessons (avoid if possible)\n                    const hasAdjacentLesson = timeIndex > 0 && weekSchedule[dayIndex][timeIndex - 1]?.subjectId === enrollment.subjectId || timeIndex < STANDARD_TIME_SLOTS.length - 1 && weekSchedule[dayIndex][timeIndex + 1]?.subjectId === enrollment.subjectId;\n                    // For subjects with multiple lessons per week, allow adjacent lessons if necessary\n                    if (!hasAdjacentLesson || enrollment.lessonsPerWeek >= 4) {\n                        subjectLessonCounts[enrollment.subjectId]++;\n                        subjectDailyCount[enrollment.subjectId][dayIndex]++;\n                        // Create lesson object with placeholder lesson number (will be fixed later)\n                        const lesson = createLessonObject(enrollment, 0, dayIndex, timeIndex, academicWeek, studentData);\n                        weekSchedule[dayIndex][timeIndex] = lesson;\n                        scheduled = true;\n                        break;\n                    }\n                }\n            }\n            if (scheduled) break;\n            // If no preferred slot worked, try any available slot on this day\n            for(let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++){\n                if (weekSchedule[dayIndex][timeIndex] === null) {\n                    subjectLessonCounts[enrollment.subjectId]++;\n                    subjectDailyCount[enrollment.subjectId][dayIndex]++;\n                    // Create lesson object with placeholder lesson number (will be fixed later)\n                    const lesson = createLessonObject(enrollment, 0, dayIndex, timeIndex, academicWeek, studentData);\n                    weekSchedule[dayIndex][timeIndex] = lesson;\n                    scheduled = true;\n                    break;\n                }\n            }\n            if (scheduled) break;\n        }\n        if (!scheduled) {\n            console.warn(`[Timetable API] Could not schedule ${enrollment.subjectName} lesson - no available slots`);\n        }\n    }\n    // Now fix lesson numbering to be chronological\n    fixLessonNumberingChronologically(weekSchedule, academicWeek, studentData);\n    // Log scheduling results\n    console.log(`[Timetable API] Scheduled lessons and fixed chronological numbering`);\n}\n// Fix lesson numbering to follow chronological order (day and time based)\nfunction fixLessonNumberingChronologically(weekSchedule, academicWeek, studentData) {\n    // Group lessons by subject\n    const subjectLessons = {};\n    // Collect all lessons for each subject\n    for(let dayIndex = 0; dayIndex < DAYS_OF_WEEK.length; dayIndex++){\n        for(let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++){\n            const lesson = weekSchedule[dayIndex][timeIndex];\n            if (lesson && lesson.subjectId && lesson.subjectId !== 'free_period') {\n                if (!subjectLessons[lesson.subjectId]) {\n                    subjectLessons[lesson.subjectId] = [];\n                }\n                subjectLessons[lesson.subjectId].push({\n                    lesson,\n                    dayIndex,\n                    timeIndex,\n                    position: dayIndex * STANDARD_TIME_SLOTS.length + timeIndex // For sorting\n                });\n            }\n        }\n    }\n    // For each subject, sort lessons chronologically and reassign lesson numbers\n    Object.entries(subjectLessons).forEach(([subjectId, lessons])=>{\n        // Sort by chronological position (day first, then time)\n        lessons.sort((a, b)=>a.position - b.position);\n        // Reassign lesson numbers chronologically\n        lessons.forEach((lessonData, index)=>{\n            const { lesson, dayIndex, timeIndex } = lessonData;\n            const newLessonNumber = index + 1;\n            // Update the lesson object with correct numbering\n            const enrollment = {\n                subjectId: lesson.subjectId,\n                subjectName: lesson.subject,\n                subjectCode: lesson.subjectCode,\n                lessonsPerWeek: lessons.length,\n                cognitiveLoad: lesson.cognitiveLoad,\n                status: 'active',\n                enrolledAt: lesson.enrolledAt || new Date()\n            };\n            const updatedLesson = createLessonObject(enrollment, newLessonNumber, dayIndex, timeIndex, academicWeek, studentData);\n            // Replace the lesson in the schedule\n            weekSchedule[dayIndex][timeIndex] = updatedLesson;\n        });\n    });\n    console.log(`[Timetable API] Fixed lesson numbering for ${Object.keys(subjectLessons).length} subjects`);\n}\n// Get preferred time slots based on cognitive load\nfunction getPreferredTimeSlots(cognitiveLoad) {\n    if (cognitiveLoad >= 8) {\n        // High cognitive load: prefer early morning slots\n        return [\n            0,\n            1,\n            2,\n            3,\n            4,\n            5,\n            6\n        ];\n    } else if (cognitiveLoad >= 6) {\n        // Medium cognitive load: prefer mid-morning slots\n        return [\n            1,\n            2,\n            3,\n            0,\n            4,\n            5,\n            6\n        ];\n    } else if (cognitiveLoad >= 4) {\n        // Low-medium cognitive load: prefer afternoon slots\n        return [\n            3,\n            4,\n            5,\n            2,\n            6,\n            1,\n            0\n        ];\n    } else {\n        // Low cognitive load: prefer late slots\n        return [\n            5,\n            6,\n            4,\n            3,\n            2,\n            1,\n            0\n        ];\n    }\n}\n// Create a lesson object\nfunction createLessonObject(enrollment, lessonNumber, dayIndex, timeIndex, academicWeek, studentData) {\n    const lessonReference = generateLessonReference(studentData.grade, enrollment.subjectCode, academicWeek, lessonNumber, enrollment.lessonsPerWeek);\n    // Debug logging to help diagnose issues\n    console.log(`[Timetable API] Creating lesson object:`, {\n        lessonReference,\n        subject: enrollment.subjectName,\n        grade: studentData.grade,\n        subjectCode: enrollment.subjectCode,\n        academicWeek,\n        lessonNumber,\n        day: DAYS_OF_WEEK[dayIndex],\n        time: STANDARD_TIME_SLOTS[timeIndex],\n        curriculumPath: getCurriculumPath(studentData, enrollment.subjectName),\n        expectedFirestorePath: `/countries/${studentData.country}/curriculums/${studentData.curriculum}/grades/${studentData.grade.replace('-', ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase())}/levels/${studentData.level}/subjects/${enrollment.subjectName.replace(/\\b\\w/g, (l)=>l.toUpperCase())}/lessonRef/${lessonReference}`\n    });\n    // Default all lessons to \"upcoming\" status\n    // Actual completion status will be applied later from Firestore data\n    const status = 'upcoming';\n    return {\n        id: `${lessonReference}_week${academicWeek}`,\n        lessonReference: lessonReference,\n        lessonRef: lessonReference,\n        title: `${enrollment.subjectName} - Week ${academicWeek}, Lesson ${lessonNumber}`,\n        subject: enrollment.subjectName,\n        subjectId: enrollment.subjectId,\n        subjectCode: enrollment.subjectCode,\n        time: STANDARD_TIME_SLOTS[timeIndex],\n        day: DAYS_OF_WEEK[dayIndex],\n        duration: 45,\n        status: status,\n        description: `Week ${academicWeek} lesson ${lessonNumber} for ${enrollment.subjectName}`,\n        grade: studentData.grade,\n        level: studentData.level,\n        curriculum: studentData.curriculum,\n        country: studentData.country,\n        academicWeek: academicWeek,\n        lessonNumberInWeek: lessonNumber,\n        absoluteLessonNumber: (academicWeek - 1) * enrollment.lessonsPerWeek + lessonNumber,\n        totalWeeks: 30,\n        teacher: 'AI Instructor',\n        cognitiveLoad: enrollment.cognitiveLoad,\n        curriculumPath: getCurriculumPath(studentData, enrollment.subjectName),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    };\n}\n// Apply lesson completion states from Firestore to the generated lessons\nfunction applyLessonCompletionStates(lessons, completionStates) {\n    console.log(`[Timetable API] Applying completion states to ${lessons.length} lessons`);\n    const updatedLessons = lessons.map((lesson)=>{\n        if (!lesson.lessonReference || lesson.isFreePeriod) {\n            return lesson; // Skip lessons without lesson references or free periods\n        }\n        // Check if this lesson has completion data from session\n        const sessionData = completionStates[lesson.lessonReference];\n        if (sessionData) {\n            console.log(`[Timetable API] Found session data for ${lesson.lessonReference}:`, {\n                status: sessionData.status,\n                sessionId: sessionData.sessionId,\n                currentPhase: sessionData.currentPhase,\n                lastUpdated: sessionData.lastUpdated\n            });\n            // Update lesson with actual completion status from session\n            return {\n                ...lesson,\n                status: sessionData.status,\n                completed: sessionData.completed,\n                completedAt: sessionData.completedAt,\n                progress: sessionData.progress,\n                // Add session-specific metadata\n                sessionId: sessionData.sessionId,\n                currentPhase: sessionData.currentPhase,\n                workingLevel: sessionData.workingLevel,\n                lastUpdated: sessionData.lastUpdated,\n                sessionCreatedAt: sessionData.createdAt\n            };\n        }\n        // No session data found, keep as \"upcoming\"\n        return lesson;\n    });\n    // Calculate status summary (excluding free periods)\n    const nonFreePeriodLessons = updatedLessons.filter((l)=>!l.isFreePeriod);\n    const completedCount = nonFreePeriodLessons.filter((l)=>l.status === 'completed').length;\n    const inProgressCount = nonFreePeriodLessons.filter((l)=>l.status === 'in_progress').length;\n    const upcomingCount = nonFreePeriodLessons.filter((l)=>l.status === 'upcoming').length;\n    console.log(`[Timetable API] Lesson status summary (${nonFreePeriodLessons.length} total): ${completedCount} completed, ${inProgressCount} in progress, ${upcomingCount} upcoming`);\n    // Log details of completed/in-progress lessons\n    const activeLessons = nonFreePeriodLessons.filter((l)=>l.status !== 'upcoming');\n    if (activeLessons.length > 0) {\n        console.log(`[Timetable API] Active lessons:`, activeLessons.map((l)=>({\n                lessonRef: l.lessonReference,\n                status: l.status,\n                phase: l.currentPhase,\n                sessionId: l.sessionId\n            })));\n    }\n    return updatedLessons;\n}\n// Get complete student data including grade, curriculum, country, etc.\nasync function getStudentData(studentId) {\n    try {\n        // Try different student ID formats\n        const studentIdFormats = [\n            studentId,\n            `andrea_ugono_33305`,\n            studentId.toLowerCase(),\n            studentId.replace(/\\s+/g, '_').toLowerCase()\n        ];\n        for (const format of studentIdFormats){\n            const db = getFirebaseDb();\n            const studentRef = db.collection('students').doc(format);\n            const studentSnap = await studentRef.get();\n            if (studentSnap.exists) {\n                const studentData = studentSnap.data();\n                console.log(`[Timetable API] Found student data for ${format}:`, {\n                    gradeLevel: studentData?.gradeLevel,\n                    curriculum: studentData?.curriculum,\n                    country: studentData?.country\n                });\n                const grade = studentData?.gradeLevel || studentData?.grade || 'primary-5';\n                const level = convertGradeToAbbreviation(grade); // Convert to P5, JSS1, etc.\n                return {\n                    grade: grade,\n                    level: level,\n                    curriculum: studentData?.curriculum || 'National Curriculum',\n                    country: studentData?.country || 'Nigeria'\n                };\n            }\n        }\n        // Default values if student not found\n        console.log(`[Timetable API] Student not found, using defaults for ${studentId}`);\n        return {\n            grade: 'primary-5',\n            level: 'P5',\n            curriculum: 'National Curriculum',\n            country: 'Nigeria'\n        };\n    } catch (error) {\n        console.error('[Timetable API] Error fetching student data:', error);\n        return {\n            grade: 'primary-5',\n            level: 'P5',\n            curriculum: 'National Curriculum',\n            country: 'Nigeria'\n        };\n    }\n}\n// Get student grade level (backward compatibility)\nasync function getStudentGrade(studentId) {\n    const studentData = await getStudentData(studentId);\n    return studentData.grade;\n}\n// Fetch lesson completion states from Firestore using session-based data structure\nasync function fetchLessonCompletionStates(studentId) {\n    try {\n        console.log(`[Timetable API] Fetching lesson completion states for student: ${studentId}`);\n        // Try different student ID formats for querying\n        const studentIdFormats = [\n            studentId,\n            `andrea_ugono_33305`,\n            studentId.toLowerCase(),\n            studentId.replace(/\\s+/g, '_').toLowerCase()\n        ];\n        const completionStates = {};\n        let totalSessionsFound = 0;\n        for (const format of studentIdFormats){\n            try {\n                console.log(`[Timetable API] Querying lesson_states collection for student_id: ${format}`);\n                // Query the lesson_states collection for all sessions belonging to this student\n                const db = getFirebaseDb();\n                const lessonStatesQuery = db.collection('lesson_states').where('student_id', '==', format);\n                const querySnapshot = await lessonStatesQuery.get();\n                if (!querySnapshot.empty) {\n                    console.log(`[Timetable API] Found ${querySnapshot.size} session(s) for student ID format: ${format}`);\n                    totalSessionsFound += querySnapshot.size;\n                    // Process each session document\n                    querySnapshot.docs.forEach((sessionDoc)=>{\n                        const sessionData = sessionDoc.data();\n                        const sessionId = sessionDoc.id;\n                        console.log(`[Timetable API] Processing session ${sessionId}:`, {\n                            lesson_ref: sessionData.lesson_ref,\n                            current_phase: sessionData.current_phase,\n                            last_updated: sessionData.last_updated,\n                            student_id: sessionData.student_id\n                        });\n                        // Extract lesson reference and completion data\n                        const lessonRef = sessionData.lesson_ref;\n                        if (lessonRef) {\n                            // Determine status from current_phase\n                            let status = 'upcoming';\n                            if (sessionData.current_phase === 'lesson_completion' || sessionData.current_phase === 'completed') {\n                                status = 'completed';\n                            } else if (sessionData.current_phase && sessionData.current_phase !== 'diagnostic_start_probe') {\n                                status = 'in_progress';\n                            }\n                            // Check if we already have data for this lesson reference\n                            const existingData = completionStates[lessonRef];\n                            const currentTimestamp = sessionData.last_updated?.toDate?.() || sessionData.last_updated || new Date(0);\n                            const existingTimestamp = existingData?.lastUpdated || new Date(0);\n                            // Use the most recent session data for this lesson reference\n                            if (!existingData || currentTimestamp > existingTimestamp) {\n                                completionStates[lessonRef] = {\n                                    status: status,\n                                    completed: status === 'completed',\n                                    completedAt: status === 'completed' ? currentTimestamp : null,\n                                    progress: status === 'completed' ? 100 : status === 'in_progress' ? 50 : 0,\n                                    sessionId: sessionId,\n                                    currentPhase: sessionData.current_phase,\n                                    workingLevel: sessionData.current_session_working_level,\n                                    lastUpdated: currentTimestamp,\n                                    createdAt: sessionData.created_at?.toDate?.() || sessionData.created_at,\n                                    studentId: sessionData.student_id,\n                                    lessonRef: lessonRef\n                                };\n                            }\n                        } else {\n                            console.warn(`[Timetable API] Session ${sessionId} missing lesson_ref field`);\n                        }\n                    });\n                    break;\n                } else {\n                    console.log(`[Timetable API] No sessions found for student ID format: ${format}`);\n                }\n            } catch (formatError) {\n                console.warn(`[Timetable API] Error querying lesson states for format ${format}:`, formatError);\n                continue;\n            }\n        }\n        const completionCount = Object.keys(completionStates).length;\n        console.log(`[Timetable API] Processed ${totalSessionsFound} session(s), found ${completionCount} unique lesson completion records`);\n        // Log summary of completion states\n        if (completionCount > 0) {\n            const statusSummary = Object.values(completionStates).reduce((acc, state)=>{\n                acc[state.status] = (acc[state.status] || 0) + 1;\n                return acc;\n            }, {});\n            console.log(`[Timetable API] Completion status summary:`, statusSummary);\n        }\n        return completionStates;\n    } catch (error) {\n        console.error('[Timetable API] Error fetching lesson completion states:', error);\n        return {};\n    }\n}\n// Verify Firebase Auth token\nasync function verifyAuthToken(request) {\n    try {\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader?.startsWith('Bearer ')) {\n            return null;\n        }\n        const idToken = authHeader.split('Bearer ')[1];\n        const decodedToken = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.getAuth)().verifyIdToken(idToken);\n        return {\n            uid: decodedToken.uid\n        };\n    } catch (error) {\n        console.error('Auth verification failed:', error);\n        return null;\n    }\n}\n// Fill any remaining empty slots with \"Free Period\"\nfunction fillEmptySlotsWithFreePeriods(weekSchedule, academicWeek, studentData) {\n    const abbreviatedGrade = convertGradeToAbbreviation(studentData.grade);\n    let freePeriodCounter = 1;\n    for(let dayIndex = 0; dayIndex < DAYS_OF_WEEK.length; dayIndex++){\n        for(let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++){\n            if (weekSchedule[dayIndex][timeIndex] === null) {\n                const freePeriod = {\n                    id: `free_period_week${academicWeek}_${DAYS_OF_WEEK[dayIndex]}_${timeIndex}`,\n                    lessonReference: `${abbreviatedGrade}-${String(academicWeek).padStart(3, '0')}-${String(freePeriodCounter).padStart(3, '0')}`,\n                    title: 'Free Period',\n                    subject: 'Free Period',\n                    subjectId: 'free_period',\n                    subjectCode: null,\n                    time: STANDARD_TIME_SLOTS[timeIndex],\n                    day: DAYS_OF_WEEK[dayIndex],\n                    duration: 45,\n                    status: 'upcoming',\n                    description: 'Free study period',\n                    grade: studentData.grade,\n                    academicWeek: academicWeek,\n                    lessonNumberInWeek: freePeriodCounter,\n                    absoluteLessonNumber: null,\n                    totalWeeks: 30,\n                    teacher: null,\n                    cognitiveLoad: 1,\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString(),\n                    isFreePeriod: true\n                };\n                weekSchedule[dayIndex][timeIndex] = freePeriod;\n                freePeriodCounter++;\n            }\n        }\n    }\n}\nasync function GET(request) {\n    try {\n        // Firebase Admin will be initialized on-demand via getFirebaseDb() calls\n        // For development/testing, allow unauthenticated requests\n        // In production, uncomment the authentication check below\n        /*\r\n    const authResult = await verifyAuthToken(request);\r\n    if (!authResult) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'Unauthorized' },\r\n        { status: 401 }\r\n      );\r\n    }\r\n    */ const { searchParams } = new URL(request.url);\n        const studentId = searchParams.get('studentId');\n        const date = searchParams.get('date');\n        const weekParam = searchParams.get('week');\n        // Parse week parameter (default to week 1)\n        const academicWeek = weekParam ? parseInt(weekParam, 10) : 1;\n        if (isNaN(academicWeek) || academicWeek < 1 || academicWeek > 30) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Invalid week number. Must be between 1 and 30.'\n            }, {\n                status: 400\n            });\n        }\n        if (!studentId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Student ID is required'\n            }, {\n                status: 400\n            });\n        }\n        console.log(`[Timetable API] Fetching timetable for student: ${studentId}, week: ${academicWeek}, date: ${date}`);\n        // Fetch complete student data including grade, curriculum, country\n        const studentData = await getStudentData(studentId);\n        console.log(`[Timetable API] Student data:`, studentData);\n        // Fetch student enrollments (this is now the single source of truth)\n        const enrollments = await fetchStudentEnrollments(studentId);\n        if (enrollments.length === 0) {\n            console.log('[Timetable API] No active enrollments found');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: {\n                    schedule: [],\n                    timetable: [],\n                    totalLessons: 0,\n                    enrollments: [],\n                    student_id: studentId,\n                    academic_week: academicWeek,\n                    total_weeks: 30,\n                    student_grade: studentData.grade,\n                    date: date\n                }\n            });\n        }\n        // Generate weekly timetable based on enrollments\n        const weeklySchedule = distributeLessonsAcrossWeek(enrollments, academicWeek, studentData);\n        // Fetch lesson completion states from Firestore\n        const completionStates = await fetchLessonCompletionStates(studentId);\n        // Apply actual completion states to the generated lessons\n        const scheduleWithCompletionStates = applyLessonCompletionStates(weeklySchedule, completionStates);\n        // Sort weekly schedule by day and time\n        const dayOrder = [\n            'Monday',\n            'Tuesday',\n            'Wednesday',\n            'Thursday',\n            'Friday'\n        ];\n        scheduleWithCompletionStates.sort((a, b)=>{\n            const dayA = dayOrder.indexOf(a.day);\n            const dayB = dayOrder.indexOf(b.day);\n            if (dayA !== dayB) {\n                return dayA - dayB;\n            }\n            // Sort by time within the same day\n            if (a.time && b.time) {\n                return a.time.localeCompare(b.time);\n            }\n            return 0;\n        });\n        console.log(`[Timetable API] Returning ${scheduleWithCompletionStates.length} lessons for week ${academicWeek}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                schedule: scheduleWithCompletionStates,\n                timetable: scheduleWithCompletionStates,\n                totalLessons: scheduleWithCompletionStates.length,\n                enrollments: enrollments,\n                student_id: studentId,\n                academic_week: academicWeek,\n                total_weeks: 30,\n                student_grade: studentData.grade,\n                date: date\n            }\n        });\n    } catch (error) {\n        console.error('[Timetable API] Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : 'Failed to fetch timetable',\n            details: error instanceof Error ? error.stack : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/timetable/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@opentelemetry/api":
/*!*************************************!*\
  !*** external "@opentelemetry/api" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@opentelemetry/api");

/***/ }),

/***/ "firebase-admin":
/*!*********************************!*\
  !*** external "firebase-admin" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("firebase-admin");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@firebase","vendor-chunks/tslib","vendor-chunks/idb","vendor-chunks/firebase"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftimetable%2Froute&page=%2Fapi%2Ftimetable%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftimetable%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();