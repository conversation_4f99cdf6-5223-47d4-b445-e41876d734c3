#!/usr/bin/env python3
"""
Quick test to verify objectives coverage calculation is working correctly.
"""

import requests
import json

def test_objectives_coverage():
    """Test the objectives coverage calculation with different interaction counts."""
    
    base_url = "http://localhost:5000"
    
    # Get test token
    token_response = requests.get(f"{base_url}/generate-test-token")
    if token_response.status_code != 200:
        print("❌ Failed to get test token")
        return False
    
    token = token_response.json()["token"]
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
        "X-Student-Id": "andrea_ugono_33305",
        "X-Testing-Mode": "true"
    }
    
    # Initialize lesson session
    lesson_data = {
        "student_id": "andrea_ugono_33305",
        "lessonRef": "P5-MAT-001",
        "country": "Nigeria",
        "curriculum": "National Curriculum",
        "grade": "Primary 5",
        "level": "P5",
        "subject": "Mathematics",
        "content_to_enhance": "Start diagnostic assessment",
        "chat_history": []
    }
    
    print("🔍 Testing Objectives Coverage Calculation")
    print("=" * 50)
    
    # Initialize session
    response = requests.post(f"{base_url}/api/enhance-content", headers=headers, json=lesson_data)
    if response.status_code != 200:
        print("❌ Failed to initialize lesson session")
        return False
    
    session_data = response.json()
    session_id = session_data.get("session_id")
    print(f"✅ Session initialized: {session_id}")
    
    # Test different teaching interaction counts
    test_interactions = [8, 10, 12, 15, 18]
    
    for interaction_count in test_interactions:
        # Simulate teaching interactions
        for i in range(interaction_count):
            enhance_data = {
                "student_id": "andrea_ugono_33305",
                "session_id": session_id,
                "content_to_enhance": f"Continue teaching interaction {i+1}",
                "chat_history": []
            }
            
            response = requests.post(f"{base_url}/api/enhance-content", headers=headers, json=enhance_data)
            if response.status_code != 200:
                print(f"❌ Failed at interaction {i+1}")
                continue
            
            result = response.json()
            current_phase = result.get("current_lesson_phase", "unknown")
            
            # Check if we've transitioned to quiz
            if current_phase == "quiz_initiate":
                print(f"🎯 QUIZ TRANSITION at interaction {i+1}!")
                return True
        
        print(f"📊 After {interaction_count} interactions: Still in teaching phase")
    
    print("❌ No quiz transition detected after 18 interactions")
    return False

if __name__ == "__main__":
    success = test_objectives_coverage()
    exit(0 if success else 1)
