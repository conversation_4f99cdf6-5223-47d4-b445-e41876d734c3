"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hastscript";
exports.ids = ["vendor-chunks/hastscript"];
exports.modules = {

/***/ "(ssr)/./node_modules/hastscript/lib/create-h.js":
/*!*************************************************!*\
  !*** ./node_modules/hastscript/lib/create-h.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createH: () => (/* binding */ createH)\n/* harmony export */ });\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var hast_util_parse_selector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-parse-selector */ \"(ssr)/./node_modules/hast-util-parse-selector/lib/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/normalize.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/space-separated-tokens/index.js\");\n/**\n * @import {Element, Nodes, RootContent, Root} from 'hast'\n * @import {Info, Schema} from 'property-information'\n */\n\n/**\n * @typedef {Array<Nodes | PrimitiveChild>} ArrayChildNested\n *   List of children (deep).\n */\n\n/**\n * @typedef {Array<ArrayChildNested | Nodes | PrimitiveChild>} ArrayChild\n *   List of children.\n */\n\n/**\n * @typedef {Array<number | string>} ArrayValue\n *   List of property values for space- or comma separated values (such as `className`).\n */\n\n/**\n * @typedef {ArrayChild | Nodes | PrimitiveChild} Child\n *   Acceptable child value.\n */\n\n/**\n * @typedef {number | string | null | undefined} PrimitiveChild\n *   Primitive children, either ignored (nullish), or turned into text nodes.\n */\n\n/**\n * @typedef {boolean | number | string | null | undefined} PrimitiveValue\n *   Primitive property value.\n */\n\n/**\n * @typedef {Record<string, PropertyValue | Style>} Properties\n *   Acceptable value for element properties.\n */\n\n/**\n * @typedef {ArrayValue | PrimitiveValue} PropertyValue\n *   Primitive value or list value.\n */\n\n/**\n * @typedef {Element | Root} Result\n *   Result from a `h` (or `s`) call.\n */\n\n/**\n * @typedef {number | string} StyleValue\n *   Value for a CSS style field.\n */\n\n/**\n * @typedef {Record<string, StyleValue>} Style\n *   Supported value of a `style` prop.\n */\n\n\n\n\n\n\n/**\n * @param {Schema} schema\n *   Schema to use.\n * @param {string} defaultTagName\n *   Default tag name.\n * @param {ReadonlyArray<string> | undefined} [caseSensitive]\n *   Case-sensitive tag names (default: `undefined`).\n * @returns\n *   `h`.\n */\nfunction createH(schema, defaultTagName, caseSensitive) {\n  const adjust = caseSensitive ? createAdjustMap(caseSensitive) : undefined\n\n  /**\n   * Hyperscript compatible DSL for creating virtual hast trees.\n   *\n   * @overload\n   * @param {null | undefined} [selector]\n   * @param {...Child} children\n   * @returns {Root}\n   *\n   * @overload\n   * @param {string} selector\n   * @param {Properties} properties\n   * @param {...Child} children\n   * @returns {Element}\n   *\n   * @overload\n   * @param {string} selector\n   * @param {...Child} children\n   * @returns {Element}\n   *\n   * @param {string | null | undefined} [selector]\n   *   Selector.\n   * @param {Child | Properties | null | undefined} [properties]\n   *   Properties (or first child) (default: `undefined`).\n   * @param {...Child} children\n   *   Children.\n   * @returns {Result}\n   *   Result.\n   */\n  function h(selector, properties, ...children) {\n    /** @type {Result} */\n    let node\n\n    if (selector === null || selector === undefined) {\n      node = {type: 'root', children: []}\n      // Properties are not supported for roots.\n      const child = /** @type {Child} */ (properties)\n      children.unshift(child)\n    } else {\n      node = (0,hast_util_parse_selector__WEBPACK_IMPORTED_MODULE_0__.parseSelector)(selector, defaultTagName)\n      // Normalize the name.\n      const lower = node.tagName.toLowerCase()\n      const adjusted = adjust ? adjust.get(lower) : undefined\n      node.tagName = adjusted || lower\n\n      // Handle properties.\n      if (isChild(properties)) {\n        children.unshift(properties)\n      } else {\n        for (const [key, value] of Object.entries(properties)) {\n          addProperty(schema, node.properties, key, value)\n        }\n      }\n    }\n\n    // Handle children.\n    for (const child of children) {\n      addChild(node.children, child)\n    }\n\n    if (node.type === 'element' && node.tagName === 'template') {\n      node.content = {type: 'root', children: node.children}\n      node.children = []\n    }\n\n    return node\n  }\n\n  return h\n}\n\n/**\n * Check if something is properties or a child.\n *\n * @param {Child | Properties} value\n *   Value to check.\n * @returns {value is Child}\n *   Whether `value` is definitely a child.\n */\nfunction isChild(value) {\n  // Never properties if not an object.\n  if (value === null || typeof value !== 'object' || Array.isArray(value)) {\n    return true\n  }\n\n  // Never node without `type`; that’s the main discriminator.\n  if (typeof value.type !== 'string') return false\n\n  // Slower check: never property value if object or array with\n  // non-number/strings.\n  const record = /** @type {Record<string, unknown>} */ (value)\n  const keys = Object.keys(value)\n\n  for (const key of keys) {\n    const value = record[key]\n\n    if (value && typeof value === 'object') {\n      if (!Array.isArray(value)) return true\n\n      const list = /** @type {ReadonlyArray<unknown>} */ (value)\n\n      for (const item of list) {\n        if (typeof item !== 'number' && typeof item !== 'string') {\n          return true\n        }\n      }\n    }\n  }\n\n  // Also see empty `children` as a node.\n  if ('children' in value && Array.isArray(value.children)) {\n    return true\n  }\n\n  // Default to properties, someone can always pass an empty object,\n  // put `data: {}` in a node,\n  // or wrap it in an array.\n  return false\n}\n\n/**\n * @param {Schema} schema\n *   Schema.\n * @param {Properties} properties\n *   Properties object.\n * @param {string} key\n *   Property name.\n * @param {PropertyValue | Style} value\n *   Property value.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addProperty(schema, properties, key, value) {\n  const info = (0,property_information__WEBPACK_IMPORTED_MODULE_1__.find)(schema, key)\n  /** @type {PropertyValue} */\n  let result\n\n  // Ignore nullish and NaN values.\n  if (value === null || value === undefined) return\n\n  if (typeof value === 'number') {\n    // Ignore NaN.\n    if (Number.isNaN(value)) return\n\n    result = value\n  }\n  // Booleans.\n  else if (typeof value === 'boolean') {\n    result = value\n  }\n  // Handle list values.\n  else if (typeof value === 'string') {\n    if (info.spaceSeparated) {\n      result = (0,space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__.parse)(value)\n    } else if (info.commaSeparated) {\n      result = (0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__.parse)(value)\n    } else if (info.commaOrSpaceSeparated) {\n      result = (0,space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__.parse)((0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__.parse)(value).join(' '))\n    } else {\n      result = parsePrimitive(info, info.property, value)\n    }\n  } else if (Array.isArray(value)) {\n    result = [...value]\n  } else {\n    result = info.property === 'style' ? style(value) : String(value)\n  }\n\n  if (Array.isArray(result)) {\n    /** @type {Array<number | string>} */\n    const finalResult = []\n\n    for (const item of result) {\n      // Assume no booleans in array.\n      finalResult.push(\n        /** @type {number | string} */ (\n          parsePrimitive(info, info.property, item)\n        )\n      )\n    }\n\n    result = finalResult\n  }\n\n  // Class names (which can be added both on the `selector` and here).\n  if (info.property === 'className' && Array.isArray(properties.className)) {\n    // Assume no booleans in `className`.\n    result = properties.className.concat(\n      /** @type {Array<number | string> | number | string} */ (result)\n    )\n  }\n\n  properties[info.property] = result\n}\n\n/**\n * @param {Array<RootContent>} nodes\n *   Children.\n * @param {Child} value\n *   Child.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addChild(nodes, value) {\n  if (value === null || value === undefined) {\n    // Empty.\n  } else if (typeof value === 'number' || typeof value === 'string') {\n    nodes.push({type: 'text', value: String(value)})\n  } else if (Array.isArray(value)) {\n    for (const child of value) {\n      addChild(nodes, child)\n    }\n  } else if (typeof value === 'object' && 'type' in value) {\n    if (value.type === 'root') {\n      addChild(nodes, value.children)\n    } else {\n      nodes.push(value)\n    }\n  } else {\n    throw new Error('Expected node, nodes, or string, got `' + value + '`')\n  }\n}\n\n/**\n * Parse a single primitives.\n *\n * @param {Info} info\n *   Property information.\n * @param {string} name\n *   Property name.\n * @param {PrimitiveValue} value\n *   Property value.\n * @returns {PrimitiveValue}\n *   Property value.\n */\nfunction parsePrimitive(info, name, value) {\n  if (typeof value === 'string') {\n    if (info.number && value && !Number.isNaN(Number(value))) {\n      return Number(value)\n    }\n\n    if (\n      (info.boolean || info.overloadedBoolean) &&\n      (value === '' || (0,property_information__WEBPACK_IMPORTED_MODULE_4__.normalize)(value) === (0,property_information__WEBPACK_IMPORTED_MODULE_4__.normalize)(name))\n    ) {\n      return true\n    }\n  }\n\n  return value\n}\n\n/**\n * Serialize a `style` object as a string.\n *\n * @param {Style} styles\n *   Style object.\n * @returns {string}\n *   CSS string.\n */\nfunction style(styles) {\n  /** @type {Array<string>} */\n  const result = []\n\n  for (const [key, value] of Object.entries(styles)) {\n    result.push([key, value].join(': '))\n  }\n\n  return result.join('; ')\n}\n\n/**\n * Create a map to adjust casing.\n *\n * @param {ReadonlyArray<string>} values\n *   List of properly cased keys.\n * @returns {Map<string, string>}\n *   Map of lowercase keys to uppercase keys.\n */\nfunction createAdjustMap(values) {\n  /** @type {Map<string, string>} */\n  const result = new Map()\n\n  for (const value of values) {\n    result.set(value.toLowerCase(), value)\n  }\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/lib/create-h.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/lib/index.js":
/*!**********************************************!*\
  !*** ./node_modules/hastscript/lib/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   h: () => (/* binding */ h),\n/* harmony export */   s: () => (/* binding */ s)\n/* harmony export */ });\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var _create_h_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./create-h.js */ \"(ssr)/./node_modules/hastscript/lib/create-h.js\");\n/* harmony import */ var _svg_case_sensitive_tag_names_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./svg-case-sensitive-tag-names.js */ \"(ssr)/./node_modules/hastscript/lib/svg-case-sensitive-tag-names.js\");\n// Register the JSX namespace on `h`.\n/**\n * @typedef {import('./jsx-classic.js').Element} h.JSX.Element\n * @typedef {import('./jsx-classic.js').ElementChildrenAttribute} h.JSX.ElementChildrenAttribute\n * @typedef {import('./jsx-classic.js').IntrinsicAttributes} h.JSX.IntrinsicAttributes\n * @typedef {import('./jsx-classic.js').IntrinsicElements} h.JSX.IntrinsicElements\n */\n\n// Register the JSX namespace on `s`.\n/**\n * @typedef {import('./jsx-classic.js').Element} s.JSX.Element\n * @typedef {import('./jsx-classic.js').ElementChildrenAttribute} s.JSX.ElementChildrenAttribute\n * @typedef {import('./jsx-classic.js').IntrinsicAttributes} s.JSX.IntrinsicAttributes\n * @typedef {import('./jsx-classic.js').IntrinsicElements} s.JSX.IntrinsicElements\n */\n\n\n\n\n\n// Note: this explicit type is needed, otherwise TS creates broken types.\n/** @type {ReturnType<createH>} */\nconst h = (0,_create_h_js__WEBPACK_IMPORTED_MODULE_0__.createH)(property_information__WEBPACK_IMPORTED_MODULE_1__.html, 'div')\n\n// Note: this explicit type is needed, otherwise TS creates broken types.\n/** @type {ReturnType<createH>} */\nconst s = (0,_create_h_js__WEBPACK_IMPORTED_MODULE_0__.createH)(property_information__WEBPACK_IMPORTED_MODULE_1__.svg, 'g', _svg_case_sensitive_tag_names_js__WEBPACK_IMPORTED_MODULE_2__.svgCaseSensitiveTagNames)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0EsYUFBYSxvQ0FBb0M7QUFDakQsYUFBYSxxREFBcUQ7QUFDbEUsYUFBYSxnREFBZ0Q7QUFDN0QsYUFBYSw4Q0FBOEM7QUFDM0Q7O0FBRUE7QUFDQTtBQUNBLGFBQWEsb0NBQW9DO0FBQ2pELGFBQWEscURBQXFEO0FBQ2xFLGFBQWEsZ0RBQWdEO0FBQzdELGFBQWEsOENBQThDO0FBQzNEOztBQUU4QztBQUNUO0FBQ3FDOztBQUUxRTtBQUNBLFdBQVcscUJBQXFCO0FBQ3pCLFVBQVUscURBQU8sQ0FBQyxzREFBSTs7QUFFN0I7QUFDQSxXQUFXLHFCQUFxQjtBQUN6QixVQUFVLHFEQUFPLENBQUMscURBQUcsT0FBTyxzRkFBd0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xcaGFzdHNjcmlwdFxcbGliXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBSZWdpc3RlciB0aGUgSlNYIG5hbWVzcGFjZSBvbiBgaGAuXG4vKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4vanN4LWNsYXNzaWMuanMnKS5FbGVtZW50fSBoLkpTWC5FbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2pzeC1jbGFzc2ljLmpzJykuRWxlbWVudENoaWxkcmVuQXR0cmlidXRlfSBoLkpTWC5FbGVtZW50Q2hpbGRyZW5BdHRyaWJ1dGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4vanN4LWNsYXNzaWMuanMnKS5JbnRyaW5zaWNBdHRyaWJ1dGVzfSBoLkpTWC5JbnRyaW5zaWNBdHRyaWJ1dGVzXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2pzeC1jbGFzc2ljLmpzJykuSW50cmluc2ljRWxlbWVudHN9IGguSlNYLkludHJpbnNpY0VsZW1lbnRzXG4gKi9cblxuLy8gUmVnaXN0ZXIgdGhlIEpTWCBuYW1lc3BhY2Ugb24gYHNgLlxuLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2pzeC1jbGFzc2ljLmpzJykuRWxlbWVudH0gcy5KU1guRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi9qc3gtY2xhc3NpYy5qcycpLkVsZW1lbnRDaGlsZHJlbkF0dHJpYnV0ZX0gcy5KU1guRWxlbWVudENoaWxkcmVuQXR0cmlidXRlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2pzeC1jbGFzc2ljLmpzJykuSW50cmluc2ljQXR0cmlidXRlc30gcy5KU1guSW50cmluc2ljQXR0cmlidXRlc1xuICogQHR5cGVkZWYge2ltcG9ydCgnLi9qc3gtY2xhc3NpYy5qcycpLkludHJpbnNpY0VsZW1lbnRzfSBzLkpTWC5JbnRyaW5zaWNFbGVtZW50c1xuICovXG5cbmltcG9ydCB7aHRtbCwgc3ZnfSBmcm9tICdwcm9wZXJ0eS1pbmZvcm1hdGlvbidcbmltcG9ydCB7Y3JlYXRlSH0gZnJvbSAnLi9jcmVhdGUtaC5qcydcbmltcG9ydCB7c3ZnQ2FzZVNlbnNpdGl2ZVRhZ05hbWVzfSBmcm9tICcuL3N2Zy1jYXNlLXNlbnNpdGl2ZS10YWctbmFtZXMuanMnXG5cbi8vIE5vdGU6IHRoaXMgZXhwbGljaXQgdHlwZSBpcyBuZWVkZWQsIG90aGVyd2lzZSBUUyBjcmVhdGVzIGJyb2tlbiB0eXBlcy5cbi8qKiBAdHlwZSB7UmV0dXJuVHlwZTxjcmVhdGVIPn0gKi9cbmV4cG9ydCBjb25zdCBoID0gY3JlYXRlSChodG1sLCAnZGl2JylcblxuLy8gTm90ZTogdGhpcyBleHBsaWNpdCB0eXBlIGlzIG5lZWRlZCwgb3RoZXJ3aXNlIFRTIGNyZWF0ZXMgYnJva2VuIHR5cGVzLlxuLyoqIEB0eXBlIHtSZXR1cm5UeXBlPGNyZWF0ZUg+fSAqL1xuZXhwb3J0IGNvbnN0IHMgPSBjcmVhdGVIKHN2ZywgJ2cnLCBzdmdDYXNlU2Vuc2l0aXZlVGFnTmFtZXMpXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/lib/svg-case-sensitive-tag-names.js":
/*!*********************************************************************!*\
  !*** ./node_modules/hastscript/lib/svg-case-sensitive-tag-names.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   svgCaseSensitiveTagNames: () => (/* binding */ svgCaseSensitiveTagNames)\n/* harmony export */ });\n/**\n * List of case-sensitive SVG tag names.\n *\n * @type {ReadonlyArray<string>}\n */\nconst svgCaseSensitiveTagNames = [\n  'altGlyph',\n  'altGlyphDef',\n  'altGlyphItem',\n  'animateColor',\n  'animateMotion',\n  'animateTransform',\n  'clipPath',\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feDropShadow',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n  'foreignObject',\n  'glyphRef',\n  'linearGradient',\n  'radialGradient',\n  'solidColor',\n  'textArea',\n  'textPath'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9saWIvc3ZnLWNhc2Utc2Vuc2l0aXZlLXRhZy1uYW1lcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwY1xcT25lRHJpdmVcXERlc2t0b3BcXERlc2t0b3BcXFNvbHludGFfV2Vic2l0ZVxcZnJvbnRlbmRcXGxlc3Nvbi1wbGF0Zm9ybVxcbm9kZV9tb2R1bGVzXFxoYXN0c2NyaXB0XFxsaWJcXHN2Zy1jYXNlLXNlbnNpdGl2ZS10YWctbmFtZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBMaXN0IG9mIGNhc2Utc2Vuc2l0aXZlIFNWRyB0YWcgbmFtZXMuXG4gKlxuICogQHR5cGUge1JlYWRvbmx5QXJyYXk8c3RyaW5nPn1cbiAqL1xuZXhwb3J0IGNvbnN0IHN2Z0Nhc2VTZW5zaXRpdmVUYWdOYW1lcyA9IFtcbiAgJ2FsdEdseXBoJyxcbiAgJ2FsdEdseXBoRGVmJyxcbiAgJ2FsdEdseXBoSXRlbScsXG4gICdhbmltYXRlQ29sb3InLFxuICAnYW5pbWF0ZU1vdGlvbicsXG4gICdhbmltYXRlVHJhbnNmb3JtJyxcbiAgJ2NsaXBQYXRoJyxcbiAgJ2ZlQmxlbmQnLFxuICAnZmVDb2xvck1hdHJpeCcsXG4gICdmZUNvbXBvbmVudFRyYW5zZmVyJyxcbiAgJ2ZlQ29tcG9zaXRlJyxcbiAgJ2ZlQ29udm9sdmVNYXRyaXgnLFxuICAnZmVEaWZmdXNlTGlnaHRpbmcnLFxuICAnZmVEaXNwbGFjZW1lbnRNYXAnLFxuICAnZmVEaXN0YW50TGlnaHQnLFxuICAnZmVEcm9wU2hhZG93JyxcbiAgJ2ZlRmxvb2QnLFxuICAnZmVGdW5jQScsXG4gICdmZUZ1bmNCJyxcbiAgJ2ZlRnVuY0cnLFxuICAnZmVGdW5jUicsXG4gICdmZUdhdXNzaWFuQmx1cicsXG4gICdmZUltYWdlJyxcbiAgJ2ZlTWVyZ2UnLFxuICAnZmVNZXJnZU5vZGUnLFxuICAnZmVNb3JwaG9sb2d5JyxcbiAgJ2ZlT2Zmc2V0JyxcbiAgJ2ZlUG9pbnRMaWdodCcsXG4gICdmZVNwZWN1bGFyTGlnaHRpbmcnLFxuICAnZmVTcG90TGlnaHQnLFxuICAnZmVUaWxlJyxcbiAgJ2ZlVHVyYnVsZW5jZScsXG4gICdmb3JlaWduT2JqZWN0JyxcbiAgJ2dseXBoUmVmJyxcbiAgJ2xpbmVhckdyYWRpZW50JyxcbiAgJ3JhZGlhbEdyYWRpZW50JyxcbiAgJ3NvbGlkQ29sb3InLFxuICAndGV4dEFyZWEnLFxuICAndGV4dFBhdGgnXG5dXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/lib/svg-case-sensitive-tag-names.js\n");

/***/ })

};
;