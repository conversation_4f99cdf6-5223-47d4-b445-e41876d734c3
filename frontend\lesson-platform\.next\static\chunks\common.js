/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["common"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=false!":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=false! ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {



/***/ }),

/***/ "(app-pages-browser)/./src/app/providers/AuthProvider.tsx":
/*!********************************************!*\
  !*** ./src/app/providers/AuthProvider.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    isAuthenticated: false,\n    studentSession: null,\n    userRole: null,\n    localEffectiveRole: null,\n    isParent: false,\n    isOperator: false,\n    currentViewRole: null,\n    setCurrentViewRole: ()=>{},\n    manualSyncWithLocalStorage: ()=>{},\n    user: null,\n    userData: null,\n    childrenData: null,\n    loading: true,\n    error: null,\n    refreshUserData: async ()=>{},\n    refreshChildrenData: async ()=>{},\n    handleLoginSuccess: async ()=>{},\n    logout: async ()=>{}\n});\nfunction AuthProvider(param) {\n    let { children } = param;\n    var _session_user, _session_user1, _session_user2, _session_user3, _session_user4, _session_user5, _session_user6, _session_user7;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [childrenData, setChildrenData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [studentSession, setStudentSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Initialize currentViewRole from localStorage\n    const [currentViewRole, setCurrentViewRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"AuthProvider.useState\": ()=>{\n            if (true) {\n                return localStorage.getItem('current_view_role');\n            }\n            return null;\n        }\n    }[\"AuthProvider.useState\"]);\n    // Enhanced setCurrentViewRole that persists the selection\n    const updateCurrentViewRole = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[updateCurrentViewRole]\": (role)=>{\n            console.log(\"AuthProvider: Updating current view role from \".concat(currentViewRole, \" to: \").concat(role));\n            setCurrentViewRole(role);\n            localStorage.setItem('current_view_role', role);\n            console.log(\"AuthProvider: Updated current view role to: \".concat(role));\n        }\n    }[\"AuthProvider.useCallback[updateCurrentViewRole]\"], [\n        currentViewRole\n    ]);\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_6__.useSession)(); // session will be of type Session | null\n    // Synchronize AuthProvider auth state with NextAuth session\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (status === 'loading') return; // keep existing loading state until session known\n            if (status === 'authenticated' && (session === null || session === void 0 ? void 0 : session.user)) {\n                var _session_user;\n                // Mark as authenticated\n                if (!isAuthenticated) {\n                    console.log('AuthProvider: NextAuth session detected – marking as authenticated');\n                    setIsAuthenticated(true);\n                }\n                // Store role from session if not set yet\n                const sessRole = (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role;\n                if (sessRole && userRole !== sessRole) {\n                    console.log('AuthProvider: Syncing userRole from NextAuth session:', sessRole);\n                    setUserRole(sessRole);\n                }\n                if (loading) {\n                    setLoading(false);\n                }\n            } else if (status === 'unauthenticated') {\n                if (isAuthenticated) {\n                    console.log('AuthProvider: Session lost – marking as unauthenticated');\n                }\n                setIsAuthenticated(false);\n                if (loading) {\n                    setLoading(false);\n                }\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        status,\n        session,\n        isAuthenticated,\n        userRole,\n        loading\n    ]);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    // Ref to remember the last NextAuth status we saw\n    const prevAuthStatusRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Computed properties for role checking\n    const isParent = userRole === 'parent' || userRole === 'both';\n    const isOperator = userRole === 'operator' || userRole === 'both';\n    // Debug logging after all hooks are declared\n    console.log('AuthProvider: Initial state', {\n        loading,\n        isAuthenticated,\n        userRole,\n        session\n    });\n    // Calculate effective role based on actual role and current view context\n    const getEffectiveRole = ()=>{\n        var _session_user, _session_user1;\n        // Get role from multiple sources, prioritizing session data\n        const actualRole = (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) || (userData === null || userData === void 0 ? void 0 : userData.role) || userRole || null;\n        console.log('AuthProvider: getEffectiveRole - Sources:', {\n            sessionRole: session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.role,\n            userDataRole: userData === null || userData === void 0 ? void 0 : userData.role,\n            userRoleState: userRole,\n            actualRole,\n            currentViewRole,\n            currentPath:  true ? window.location.pathname : 0\n        });\n        if (actualRole === 'both') {\n            // For dual-role users, use the current view role or intelligently determine from path\n            const currentPath =  true ? window.location.pathname : 0;\n            // If on operator path but no view role set, assume operator intent\n            if (currentPath.startsWith('/operator') && !currentViewRole) {\n                console.log('AuthProvider: On operator path with no view role, assuming operator intent');\n                return 'operator';\n            }\n            // If on parent/dashboard path but no view role set, assume parent intent  \n            if ((currentPath === '/dashboard' || currentPath.startsWith('/student-dashboard/')) && !currentViewRole) {\n                console.log('AuthProvider: On parent path with no view role, assuming parent intent');\n                return 'parent';\n            }\n            // Use the current view role or default to parent\n            return currentViewRole || 'parent';\n        }\n        return actualRole;\n    };\n    const manualSyncWithLocalStorage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[manualSyncWithLocalStorage]\": ()=>{\n            const storedUserData = localStorage.getItem('user_data');\n            if (storedUserData) {\n                setUserData(JSON.parse(storedUserData));\n            }\n            const storedChildrenData = localStorage.getItem('children_data');\n            if (storedChildrenData) {\n                setChildrenData(JSON.parse(storedChildrenData));\n            }\n        }\n    }[\"AuthProvider.useCallback[manualSyncWithLocalStorage]\"], []);\n    const refreshUserData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[refreshUserData]\": async ()=>{\n            if (!(user === null || user === void 0 ? void 0 : user.uid)) {\n                console.log(\"AuthProvider: refreshUserData - No user.uid, exiting.\");\n                setUserData(null);\n                setUserRole(null);\n                setCurrentViewRole(null);\n                return;\n            }\n            console.log(\"AuthProvider: Refreshing user data for UID: \".concat(user.uid));\n            setError(null);\n            try {\n                // First, check custom claims for role information\n                const token = await user.getIdToken(true); // Force refresh\n                const decodedToken = await user.getIdTokenResult();\n                const customRole = decodedToken.claims.role;\n                const isParentRole = decodedToken.claims.isParent;\n                const isOperatorRole = decodedToken.claims.isOperator;\n                console.log('AuthProvider: Token claims:', {\n                    customRole,\n                    isParentRole,\n                    isOperatorRole\n                });\n                // Determine composite role\n                let finalRole = customRole;\n                if (isParentRole && isOperatorRole) {\n                    finalRole = 'both';\n                    console.log('AuthProvider: User has both parent and operator roles');\n                } else if (isParentRole) {\n                    finalRole = 'parent';\n                } else if (isOperatorRole) {\n                    finalRole = 'operator';\n                }\n                setUserRole(finalRole);\n                localStorage.setItem('user_role', finalRole);\n                // Set default view role if not set\n                if (!currentViewRole) {\n                    if (finalRole === 'both') {\n                        // Default to parent view for users with both roles\n                        setCurrentViewRole('parent');\n                        console.log('AuthProvider: Setting default view to parent for dual-role user');\n                    } else if (finalRole === 'parent' || finalRole === 'operator') {\n                        setCurrentViewRole(finalRole);\n                    }\n                }\n            // ...existing role-specific data fetching logic...\n            } catch (err) {\n                console.error('AuthProvider: Error during refreshUserData:', err);\n                setError('Failed to refresh user data. Please try logging in again.');\n                setUserData(null);\n                setUserRole(null);\n                setCurrentViewRole(null);\n            }\n        }\n    }[\"AuthProvider.useCallback[refreshUserData]\"], [\n        user\n    ]);\n    const refreshChildrenData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[refreshChildrenData]\": async ()=>{\n            if (!(user === null || user === void 0 ? void 0 : user.uid)) return;\n            try {\n                // Fetch parent dashboard which should include children data\n                const dashboardResponse = await fetch(\"/api/parent/dashboard?parentId=\".concat(user.uid));\n                const dashboardData = await dashboardResponse.json();\n                if (dashboardResponse.ok && dashboardData.success) {\n                    var _dashboardData_data;\n                    const children = dashboardData.children || ((_dashboardData_data = dashboardData.data) === null || _dashboardData_data === void 0 ? void 0 : _dashboardData_data.children) || [];\n                    setChildrenData(children);\n                    localStorage.setItem('children_data', JSON.stringify(children));\n                    // Keep a simple list of child ids in the user's doc for quick look-ups\n                    if (children.length > 0) {\n                        const parentRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, 'users', user.uid);\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.updateDoc)(parentRef, {\n                            children: children.map({\n                                \"AuthProvider.useCallback[refreshChildrenData]\": (child)=>child.id\n                            }[\"AuthProvider.useCallback[refreshChildrenData]\"])\n                        });\n                    }\n                    return; // done\n                }\n                // Fallback: dedicated children endpoint\n                const childrenResponse = await fetch(\"/api/parent/get-children?parentId=\".concat(user.uid));\n                const childrenResData = await childrenResponse.json();\n                if (childrenResponse.ok && childrenResData.success) {\n                    const children = childrenResData.children || [];\n                    setChildrenData(children);\n                    localStorage.setItem('children_data', JSON.stringify(children));\n                    if (children.length > 0) {\n                        const parentRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, 'users', user.uid);\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.updateDoc)(parentRef, {\n                            children: children.map({\n                                \"AuthProvider.useCallback[refreshChildrenData]\": (child)=>child.id\n                            }[\"AuthProvider.useCallback[refreshChildrenData]\"])\n                        });\n                    }\n                } else {\n                    const msg = childrenResData.error || 'Failed to fetch children data';\n                    throw new Error(msg);\n                }\n            } catch (err) {\n                console.error('AuthProvider: Error fetching children data:', err);\n                setError(err instanceof Error ? err.message : 'Failed to fetch children data');\n            }\n        }\n    }[\"AuthProvider.useCallback[refreshChildrenData]\"], [\n        user\n    ]);\n    const handleLoginSuccess = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[handleLoginSuccess]\": async (customToken, studentIdHint)=>{\n            // *** REMOVE ALERT ***\n            // alert(\"AuthProvider: handleLoginSuccess CALLED!\"); \n            // *** END REMOVE ALERT ***\n            console.log(\"AuthProvider: handleLoginSuccess called.\"); // Log start\n            setLoading(true);\n            setError(null);\n            try {\n                var _auth_currentUser, _auth_currentUser1;\n                // Validate token format\n                if (!customToken || typeof customToken !== 'string') {\n                    throw new Error('Invalid token format: Token is missing or not a string.');\n                }\n                if (!customToken.includes('.')) {\n                    throw new Error('Invalid token format: Token does not appear to be a valid JWT.');\n                }\n                if (customToken.length < 50) {\n                    throw new Error('Invalid token format: Token is too short to be valid.');\n                } // Sign in using the custom token with timeout\n                console.log(\"AuthProvider: Calling signInWithCustomToken...\"); // Log before call\n                const signInPromise = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithCustomToken)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, customToken);\n                const timeoutPromise = new Promise({\n                    \"AuthProvider.useCallback[handleLoginSuccess]\": (_, reject)=>setTimeout({\n                            \"AuthProvider.useCallback[handleLoginSuccess]\": ()=>reject(new Error('Sign-in timeout: The operation took too long to complete'))\n                        }[\"AuthProvider.useCallback[handleLoginSuccess]\"], 30000)\n                }[\"AuthProvider.useCallback[handleLoginSuccess]\"]);\n                const userCredential = await Promise.race([\n                    signInPromise,\n                    timeoutPromise\n                ]);\n                const loggedInUser = userCredential.user;\n                console.log(\"AuthProvider: signInWithCustomToken successful. User:\", loggedInUser.uid);\n                // *** ADD LOGGING: Check auth.currentUser immediately after sign-in ***\n                console.log(\"AuthProvider: auth.currentUser?.uid immediately after signInWithCustomToken: \".concat((_auth_currentUser = _lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth.currentUser) === null || _auth_currentUser === void 0 ? void 0 : _auth_currentUser.uid));\n                // Force token refresh to get custom claims\n                console.log(\"AuthProvider: Forcing token refresh (getIdToken(true))...\"); // Log before refresh\n                await loggedInUser.getIdToken(true);\n                console.log(\"AuthProvider: Token refresh complete.\"); // Log after refresh\n                // *** ADD LOGGING: Check auth.currentUser after token refresh ***\n                console.log(\"AuthProvider: auth.currentUser?.uid after token refresh: \".concat((_auth_currentUser1 = _lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth.currentUser) === null || _auth_currentUser1 === void 0 ? void 0 : _auth_currentUser1.uid));\n                // Use provided studentId or look in localStorage - BUT DO NOT SET AS SESSION ID\n                const studentId = studentIdHint || localStorage.getItem('student_id');\n                if (studentId) {\n                    // Only store the student ID, not as a session ID\n                    localStorage.setItem('student_id', studentId);\n                    // Remove any existing incorrect session ID that might be the student ID\n                    if (localStorage.getItem('current_session') === studentId) {\n                        console.log(\"AuthProvider: Removing incorrect session ID (was set to student ID)\");\n                        localStorage.removeItem('current_session');\n                    }\n                    // Set claims via API immediately after login\n                    try {\n                        const idToken = await loggedInUser.getIdToken();\n                        const claimResponse = await fetch('/api/auth/set-student-claims', {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json',\n                                'Authorization': \"Bearer \".concat(idToken)\n                            },\n                            body: JSON.stringify({\n                                studentId: studentId\n                            })\n                        });\n                        if (claimResponse.ok) {\n                            console.log(\"AuthProvider: Student claims set successfully\");\n                            // Force another token refresh to get the new claims\n                            await loggedInUser.getIdToken(true);\n                        } else {\n                            let errorMessage = \"Status: \".concat(claimResponse.status);\n                            try {\n                                const errorData = await claimResponse.json();\n                                errorMessage = errorData.error || errorMessage;\n                            } catch (jsonError) {\n                                // If response is not JSON, try to get text\n                                try {\n                                    const errorText = await claimResponse.text();\n                                    errorMessage = errorText || errorMessage;\n                                } catch (textError) {\n                                    console.error(\"AuthProvider: Could not parse error response:\", textError);\n                                }\n                            }\n                            console.error(\"AuthProvider: Failed to set student claims:\", errorMessage);\n                        }\n                    } catch (e) {\n                        console.error(\"AuthProvider: Error setting student claims:\", e);\n                    }\n                }\n                // Clear any parent-related flags first\n                localStorage.removeItem('parent_id');\n                localStorage.removeItem('parent_name');\n                localStorage.removeItem('parent_role');\n                localStorage.removeItem('viewing_as_child');\n                localStorage.removeItem('is_parent');\n                // Set user role and auth state\n                localStorage.setItem('user_role', 'student');\n                setUserRole('student');\n                setIsAuthenticated(true);\n                // Clear the progress flag after successful sign-in\n                localStorage.removeItem('login_in_progress');\n                console.log(\"AuthProvider: Login successful, cleared login_in_progress flag.\");\n            } catch (err) {\n                console.error(\"AuthProvider: Error signing in with custom token:\", err);\n                let errorMessage = \"Failed to sign in with custom token.\";\n                if (err instanceof Error) {\n                    if (err.message.includes('auth/quota-exceeded')) {\n                        errorMessage = \"Authentication quota exceeded. Please try again later.\";\n                    } else if (err.message.includes('auth/invalid-custom-token')) {\n                        errorMessage = \"Invalid authentication token. Please try logging in again.\";\n                    } else if (err.message.includes('auth/custom-token-mismatch')) {\n                        errorMessage = \"Authentication token mismatch. Please try logging in again.\";\n                    } else if (err.message.includes('auth/network-request-failed')) {\n                        errorMessage = \"Network error. Please check your internet connection and try again.\";\n                    } else {\n                        errorMessage = err.message;\n                    }\n                }\n                setError(errorMessage);\n                setUser(null);\n                setUserData(null);\n                setChildrenData(null);\n                setUserRole(null);\n                setStudentSession(null);\n                // Clear flags/storage on error\n                localStorage.removeItem('auth_token');\n                localStorage.removeItem('student_id');\n                localStorage.removeItem('user_role');\n                localStorage.removeItem('current_session');\n                localStorage.removeItem('login_in_progress');\n                // Re-throw the error so it can be caught by the caller\n                throw err;\n            } finally{\n                // setLoading(false); // Loading should be set to false by the onAuthStateChanged listener handling\n                console.log(\"AuthProvider: handleLoginSuccess finally block.\"); // Log finally\n            }\n        }\n    }[\"AuthProvider.useCallback[handleLoginSuccess]\"], []); // Removed auth dependency as it's globally available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            console.log(\"AuthProvider: Setting up onAuthStateChanged listener.\"); // Log listener setup\n            const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, {\n                \"AuthProvider.useEffect.unsubscribe\": async (user)=>{\n                    // *** ADD DETAILED LOGGING for onAuthStateChanged ***\n                    const timestamp = new Date().toISOString();\n                    console.log(\"AuthProvider: onAuthStateChanged fired at \".concat(timestamp, \". User object:\"), user ? {\n                        uid: user.uid,\n                        email: user.email\n                    } : null);\n                    setUser(user); // Update the user state\n                    if (user) {\n                        var _JSON_parse;\n                        console.log(\"AuthProvider: onAuthStateChanged - User is present (UID: \".concat(user.uid, \").\"));\n                        // Try to refresh token to get latest claims\n                        try {\n                            console.log(\"AuthProvider: onAuthStateChanged - Refreshing token...\");\n                            await user.getIdToken(true);\n                            console.log(\"AuthProvider: onAuthStateChanged - Token refreshed.\");\n                            // ... check claims ...\n                            const idTokenResult = await user.getIdTokenResult();\n                            console.log(\"AuthProvider: onAuthStateChanged - Token claims:\", idTokenResult.claims);\n                        // ... set role/session from claims ...\n                        } catch (e) {\n                            console.error(\"AuthProvider: onAuthStateChanged - Error refreshing token:\", e);\n                        }\n                        // Try to load from localStorage first for faster initial render\n                        const storedUserData = localStorage.getItem('user_data');\n                        if (storedUserData) {\n                            setUserData(JSON.parse(storedUserData));\n                        }\n                        const storedChildrenData = localStorage.getItem('children_data');\n                        if (storedChildrenData) {\n                            setChildrenData(JSON.parse(storedChildrenData));\n                        }\n                        // Then refresh from server\n                        console.log(\"AuthProvider: onAuthStateChanged - Calling refreshUserData...\");\n                        await refreshUserData();\n                        console.log(\"AuthProvider: onAuthStateChanged - refreshUserData complete.\");\n                        setIsAuthenticated(true);\n                        console.log(\"AuthProvider: onAuthStateChanged - Set isAuthenticated = true.\");\n                        // Only refresh children data if user is a parent\n                        if ((userData === null || userData === void 0 ? void 0 : userData.role) === 'parent' || ((_JSON_parse = JSON.parse(storedUserData || '{}')) === null || _JSON_parse === void 0 ? void 0 : _JSON_parse.role) === 'parent') {\n                            await refreshChildrenData();\n                        }\n                    } else {\n                        console.log(\"AuthProvider: onAuthStateChanged - User is null.\");\n                        // ... clear user state and localStorage ...\n                        setUserData(null);\n                        setChildrenData(null);\n                        setUserRole(null);\n                        setStudentSession(null);\n                        setIsAuthenticated(false);\n                        localStorage.removeItem('user_data');\n                        localStorage.removeItem('children_data');\n                        localStorage.removeItem('CURRENT_SESSION_KEY'); // Ensure correct key if used elsewhere\n                        localStorage.removeItem('user_role');\n                        localStorage.removeItem('student_id');\n                        console.log(\"AuthProvider: onAuthStateChanged - Cleared state and localStorage.\");\n                    }\n                    console.log(\"AuthProvider: onAuthStateChanged - Setting loading = false at \".concat(new Date().toISOString(), \".\"));\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.unsubscribe\"]);\n            // Cleanup function\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    console.log(\"AuthProvider: Cleaning up onAuthStateChanged listener.\"); // Log cleanup\n                    unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        refreshUserData,\n        refreshChildrenData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Skip if still loading auth or session\n            if (loading || status === 'loading') return;\n            // Handle user authentication state for both Firebase and NextAuth\n            const sessionUser = session === null || session === void 0 ? void 0 : session.user;\n            const isAuthenticated = !!user && !!user.uid || status === 'authenticated';\n            if (isAuthenticated) {\n                // If user is authenticated but no role is set, attempt to determine role\n                if (!userRole) {\n                    console.log(\"AuthProvider: User authenticated but no role set. Attempting to determine role...\");\n                    // First check NextAuth session for role (for parents/operators)\n                    if (status === 'authenticated' && (sessionUser === null || sessionUser === void 0 ? void 0 : sessionUser.role)) {\n                        console.log(\"AuthProvider: Found role in NextAuth session: \".concat(sessionUser.role));\n                        // For NextAuth users, get comprehensive role information from API\n                        const fetchUserRole = {\n                            \"AuthProvider.useEffect.fetchUserRole\": async ()=>{\n                                try {\n                                    console.log('AuthProvider: Attempting to fetch user role from API...');\n                                    const response = await fetch('/api/auth/get-user-role', {\n                                        method: 'GET',\n                                        credentials: 'include',\n                                        headers: {\n                                            'Content-Type': 'application/json'\n                                        }\n                                    });\n                                    console.log('AuthProvider: API response status:', response.status);\n                                    if (response.ok) {\n                                        const roleData = await response.json();\n                                        if (roleData.success) {\n                                            console.log(\"AuthProvider: API returned role: \".concat(roleData.role), roleData);\n                                            setUserRole(roleData.role);\n                                            localStorage.setItem('user_role', roleData.role);\n                                            // Set current view role based on the comprehensive role\n                                            if (roleData.role === 'both') {\n                                                // For dual-role users, check if there's a preferred view stored or default to parent\n                                                const storedViewRole = localStorage.getItem('current_view_role');\n                                                const preferredView = storedViewRole || 'parent';\n                                                setCurrentViewRole(preferredView);\n                                                localStorage.setItem('current_view_role', preferredView);\n                                                console.log(\"AuthProvider: Set view role to \".concat(preferredView, \" for dual-role user\"));\n                                            } else if (roleData.role === 'parent' || roleData.role === 'operator') {\n                                                setCurrentViewRole(roleData.role);\n                                                localStorage.setItem('current_view_role', roleData.role);\n                                            }\n                                            return;\n                                        } else {\n                                            console.log('AuthProvider: API returned unsuccessful response:', roleData);\n                                        }\n                                    } else {\n                                        console.log('AuthProvider: API returned error status:', response.status);\n                                    }\n                                    // Fallback to session role if API fails\n                                    console.log('AuthProvider: API failed, using session role as fallback');\n                                    if (sessionUser === null || sessionUser === void 0 ? void 0 : sessionUser.role) {\n                                        setUserRole(sessionUser.role);\n                                        localStorage.setItem('user_role', sessionUser.role);\n                                        // Set view role for session fallback\n                                        if (sessionUser.role === 'both') {\n                                            const storedViewRole = localStorage.getItem('current_view_role');\n                                            const preferredView = storedViewRole || 'parent';\n                                            setCurrentViewRole(preferredView);\n                                            localStorage.setItem('current_view_role', preferredView);\n                                        } else if (sessionUser.role === 'parent' || sessionUser.role === 'operator') {\n                                            setCurrentViewRole(sessionUser.role);\n                                            localStorage.setItem('current_view_role', sessionUser.role);\n                                        }\n                                    }\n                                } catch (error) {\n                                    console.error('AuthProvider: Error fetching user role:', error);\n                                    // Fallback to session role on network error\n                                    if (sessionUser === null || sessionUser === void 0 ? void 0 : sessionUser.role) {\n                                        console.log('AuthProvider: Using session role as fallback due to error');\n                                        setUserRole(sessionUser.role);\n                                        localStorage.setItem('user_role', sessionUser.role);\n                                        // Set view role for error fallback\n                                        if (sessionUser.role === 'both') {\n                                            const storedViewRole = localStorage.getItem('current_view_role');\n                                            const preferredView = storedViewRole || 'parent';\n                                            setCurrentViewRole(preferredView);\n                                            localStorage.setItem('current_view_role', preferredView);\n                                        } else if (sessionUser.role === 'parent' || sessionUser.role === 'operator') {\n                                            setCurrentViewRole(sessionUser.role);\n                                            localStorage.setItem('current_view_role', sessionUser.role);\n                                        }\n                                    }\n                                }\n                            }\n                        }[\"AuthProvider.useEffect.fetchUserRole\"];\n                        fetchUserRole();\n                        return;\n                    }\n                    // Check for role in localStorage\n                    const storedRole = localStorage.getItem('user_role');\n                    if (storedRole) {\n                        console.log(\"AuthProvider: Found role in localStorage: \".concat(storedRole));\n                        setUserRole(storedRole);\n                        // If this is a student, check for student ID\n                        if (storedRole === 'student') {\n                            const storedStudentId = localStorage.getItem('student_id') || localStorage.getItem('current_session');\n                            if (storedStudentId) {\n                                console.log(\"AuthProvider: Found student ID in localStorage: \".concat(storedStudentId));\n                                setStudentSession(storedStudentId);\n                            }\n                        }\n                    } else if (user) {\n                        // Firebase user specific logic\n                        // Check for parent indicators\n                        const isParent = localStorage.getItem('parent_id') || localStorage.getItem('parent_name');\n                        // Check for temporary student session (parent viewing student dashboard)\n                        const tempStudentSession = localStorage.getItem('temp_student_session');\n                        if (isParent) {\n                            console.log(\"AuthProvider: User appears to be a parent based on localStorage\");\n                            setUserRole('parent');\n                            // If parent is viewing a student dashboard, set the student session\n                            if (tempStudentSession) {\n                                console.log(\"AuthProvider: Parent viewing student dashboard for: \".concat(tempStudentSession));\n                                setStudentSession(tempStudentSession);\n                            }\n                        } else {\n                            // Default to student role if no other indicators\n                            console.log(\"AuthProvider: No role indicators found, defaulting to student\");\n                            setUserRole('student');\n                            // Try to find student ID\n                            const storedStudentId = localStorage.getItem('student_id') || localStorage.getItem('current_session');\n                            if (storedStudentId) {\n                                console.log(\"AuthProvider: Found student ID: \".concat(storedStudentId));\n                                setStudentSession(storedStudentId);\n                            } else {\n                                console.log(\"AuthProvider: No student ID found, using UID: \".concat(user.uid));\n                                setStudentSession(user.uid);\n                                localStorage.setItem('student_id', user.uid);\n                                localStorage.setItem('current_session', user.uid);\n                            }\n                        }\n                    }\n                }\n            // End of role determination logic\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        loading,\n        user,\n        userRole,\n        setUserRole,\n        setStudentSession,\n        status,\n        session\n    ]);\n    // ---------------------------\n    // SECOND useEffect – Navigation / Redirection logic\n    // ---------------------------\n    // ---------------------------\n    // REVISED Navigation / Redirection effect\n    // This replaces the old, complex useEffect with a simpler, more declarative approach\n    // ---------------------------\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            var _session_user, _session_user1, _session_user2, _session_user3, _session_user4;\n            // Wait until auth state & Next-Auth session have finished loading\n            if (loading || status === 'loading') {\n                console.log('AuthProvider Nav: Waiting for auth to complete...', {\n                    loading,\n                    status\n                });\n                return;\n            }\n            const currentPath =  true ? window.location.pathname : 0;\n            const sessionRole = session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role;\n            const isNextAuthAuthenticated = status === 'authenticated' && !!sessionRole;\n            const hasSessionWithRole = !!session && !!(session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.role);\n            console.log('AuthProvider Nav: DEBUG', {\n                currentPath,\n                loading,\n                status,\n                isNextAuthAuthenticated,\n                hasSessionWithRole,\n                sessionRole,\n                currentViewRole,\n                session: !!session,\n                sessionUser: session === null || session === void 0 ? void 0 : session.user,\n                firebaseUser: !!user,\n                sessionUserRole: session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : _session_user2.role,\n                rawSession: session\n            });\n            // --- 1. Handle Unauthenticated Users ---\n            // Updated authentication check to be more robust\n            const isReallyAuthenticated = isNextAuthAuthenticated || hasSessionWithRole;\n            console.log('AuthProvider Nav: Authentication status:', {\n                isNextAuthAuthenticated,\n                hasSessionWithRole,\n                isReallyAuthenticated,\n                statusCheck: status === 'authenticated',\n                sessionRoleCheck: !!sessionRole,\n                hasSessionCheck: !!session,\n                sessionUserRoleCheck: !!(session === null || session === void 0 ? void 0 : (_session_user3 = session.user) === null || _session_user3 === void 0 ? void 0 : _session_user3.role)\n            });\n            // SPECIAL CASE: If we're on any dashboard path and have any session, don't redirect\n            // EXCEPT for dual-role users who need navigation between dashboards\n            if ((currentPath.startsWith('/operator') || currentPath === '/dashboard' || currentPath.startsWith('/dashboard/')) && session && (session === null || session === void 0 ? void 0 : (_session_user4 = session.user) === null || _session_user4 === void 0 ? void 0 : _session_user4.role)) {\n                // Allow dual-role navigation to proceed\n                if (sessionRole === 'both') {\n                    console.log('AuthProvider Nav: On dashboard path with dual-role session, allowing dual-role navigation logic');\n                // Don't return here - let dual-role navigation logic run\n                } else {\n                    console.log('AuthProvider Nav: On dashboard path with valid session, allowing access');\n                    return;\n                }\n            }\n            if (!isReallyAuthenticated) {\n                const publicPaths = [\n                    '/login',\n                    '/register',\n                    '/forgot-password',\n                    '/reset'\n                ];\n                // If not on a public path, redirect to login.\n                if (!publicPaths.some({\n                    \"AuthProvider.useEffect\": (p)=>currentPath.startsWith(p)\n                }[\"AuthProvider.useEffect\"])) {\n                    console.log(\"AuthProvider Nav: Not authenticated, redirecting from \".concat(currentPath, \" to /login\"));\n                    console.log('AuthProvider Nav: Redirect reason - authentication check failed', {\n                        currentPath,\n                        isNextAuthAuthenticated,\n                        hasSessionWithRole,\n                        session: session,\n                        sessionUser: session === null || session === void 0 ? void 0 : session.user,\n                        status,\n                        sessionRole\n                    });\n                    router.replace('/login');\n                } else {\n                    console.log('AuthProvider Nav: On public path, no redirect needed');\n                }\n                return;\n            }\n            console.log('AuthProvider Nav: User is authenticated, proceeding with navigation logic');\n            // --- 2. Handle Authenticated Users ---\n            // If user is on the login page, get them out of there.\n            if (currentPath.startsWith('/login')) {\n                // For dual-role users, go to the dashboard matching their selected view.\n                const destination = sessionRole === 'both' ? currentViewRole === 'operator' ? '/operator/dashboard' : '/dashboard' : '/dashboard'; // Default for parents\n                console.log(\"AuthProvider Nav: Authenticated on login page, redirecting to \".concat(destination));\n                router.replace(destination);\n                return;\n            }\n            // --- 3. Handle Dual-Role Navigation ---\n            // This is the declarative navigation that replaces the setTimeout logic.\n            if (sessionRole === 'both') {\n                // If the user's intent (currentViewRole) doesn't match their location, navigate them.\n                if (currentViewRole === 'operator' && !currentPath.startsWith('/operator')) {\n                    console.log(\"AuthProvider Nav: Dual-role user wants 'operator' view, navigating to operator dashboard.\");\n                    router.replace('/operator/dashboard');\n                    return; // Stop further processing after navigation\n                }\n                if (currentViewRole === 'parent' && currentPath.startsWith('/operator')) {\n                    console.log(\"AuthProvider Nav: Dual-role user wants 'parent' view, navigating to parent dashboard.\");\n                    router.replace('/dashboard');\n                    return; // Stop further processing after navigation\n                }\n                // If a dual-role user is on either of their valid dashboards, DO NOTHING. Let them be.\n                console.log(\"AuthProvider Nav: Dual-role user on a valid path, no redirect needed.\");\n                return;\n            }\n            // --- 4. Handle Single-Role Users on Wrong Page ---\n            // This logic now only applies to users who are NOT dual-role.\n            if (sessionRole === 'parent' && currentPath.startsWith('/operator')) {\n                console.log(\"AuthProvider Nav: Parent on operator page, redirecting to /dashboard.\");\n                router.replace('/dashboard');\n            }\n        // Note: A single-role 'operator' would be handled by the logic in step 2 if they land on '/'.\n        // This covers all critical cases.\n        }\n    }[\"AuthProvider.useEffect\"], [\n        loading,\n        status,\n        session,\n        currentViewRole,\n        router\n    ]); // Dependencies that drive navigation\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            userData,\n            childrenData,\n            loading,\n            error,\n            isAuthenticated: !!user && !!user.uid || status === 'authenticated' || !!session && !!(session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role),\n            // Use SessionUser type for fallbacks\n            studentSession: studentSession || ((session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.role) === 'student' ? session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : _session_user2.id : null),\n            userRole: (session === null || session === void 0 ? void 0 : (_session_user3 = session.user) === null || _session_user3 === void 0 ? void 0 : _session_user3.role) || (userData === null || userData === void 0 ? void 0 : userData.role) || userRole || null,\n            localEffectiveRole: getEffectiveRole(),\n            isParent: ((session === null || session === void 0 ? void 0 : (_session_user4 = session.user) === null || _session_user4 === void 0 ? void 0 : _session_user4.role) || (userData === null || userData === void 0 ? void 0 : userData.role) || userRole) === 'parent' || ((session === null || session === void 0 ? void 0 : (_session_user5 = session.user) === null || _session_user5 === void 0 ? void 0 : _session_user5.role) || (userData === null || userData === void 0 ? void 0 : userData.role) || userRole) === 'both',\n            isOperator: ((session === null || session === void 0 ? void 0 : (_session_user6 = session.user) === null || _session_user6 === void 0 ? void 0 : _session_user6.role) || (userData === null || userData === void 0 ? void 0 : userData.role) || userRole) === 'operator' || ((session === null || session === void 0 ? void 0 : (_session_user7 = session.user) === null || _session_user7 === void 0 ? void 0 : _session_user7.role) || (userData === null || userData === void 0 ? void 0 : userData.role) || userRole) === 'both',\n            currentViewRole,\n            setCurrentViewRole: updateCurrentViewRole,\n            manualSyncWithLocalStorage,\n            refreshUserData,\n            refreshChildrenData,\n            handleLoginSuccess,\n            logout: async ()=>{\n                console.log(\"AuthProvider: Logging out...\");\n                try {\n                    await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signOut)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth);\n                    // Clear backend performance caches\n                    try {\n                        const cacheResponse = await fetch('/api/clear-performance-cache', {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            }\n                        });\n                        if (cacheResponse.ok) {\n                            console.log(\"AuthProvider: Backend caches cleared successfully\");\n                        } else {\n                            console.warn(\"AuthProvider: Failed to clear backend caches, but continuing with logout\");\n                        }\n                    } catch (cacheError) {\n                        console.warn(\"AuthProvider: Error clearing backend caches:\", cacheError);\n                    // Don't fail logout due to cache clearing issues\n                    }\n                    // Clear local state and storage\n                    setUser(null);\n                    setUserData(null);\n                    setChildrenData(null);\n                    setUserRole(null);\n                    setStudentSession(null);\n                    setCurrentViewRole(null);\n                    setIsAuthenticated(false);\n                    // Clear all browser storage\n                    localStorage.clear();\n                    sessionStorage.clear();\n                    // Clear IndexedDB (if any data is stored there)\n                    try {\n                        if ('indexedDB' in window) {\n                            const databases = await indexedDB.databases();\n                            databases.forEach(async (db)=>{\n                                if (db.name) {\n                                    indexedDB.deleteDatabase(db.name);\n                                }\n                            });\n                        }\n                    } catch (idbError) {\n                        console.warn(\"AuthProvider: Error clearing IndexedDB:\", idbError);\n                    }\n                    // Clear service worker caches\n                    try {\n                        if ('caches' in window) {\n                            const cacheNames = await caches.keys();\n                            await Promise.all(cacheNames.map((cacheName)=>caches.delete(cacheName)));\n                            console.log(\"AuthProvider: Service worker caches cleared\");\n                        }\n                    } catch (swError) {\n                        console.warn(\"AuthProvider: Error clearing service worker caches:\", swError);\n                    }\n                    console.log(\"AuthProvider: Logout successful. All caches cleared. Redirecting to login.\");\n                    router.push('/login');\n                } catch (err) {\n                    console.error(\"AuthProvider: Logout failed:\", err);\n                    setError(\"Logout failed. Please try again.\");\n                    // Still attempt to clear state/storage even if signOut fails\n                    setUser(null);\n                    setUserData(null);\n                    setChildrenData(null);\n                    setUserRole(null);\n                    setStudentSession(null);\n                    setCurrentViewRole(null);\n                    setIsAuthenticated(false);\n                    localStorage.clear();\n                    sessionStorage.clear();\n                    router.push('/login'); // Redirect even on error\n                }\n            }\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\AuthProvider.tsx\",\n        lineNumber: 797,\n        columnNumber: 3\n    }, this);\n}\n_s(AuthProvider, \"5MyXs0/f6QtqgTa9CHUv2QLUYOs=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_6__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = AuthProvider;\nconst useAuth = ()=>{\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n};\n_s1(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/providers/AuthProvider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/providers/ClientToastWrapper.tsx":
/*!**************************************************!*\
  !*** ./src/app/providers/ClientToastWrapper.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientToastWrapper: () => (/* binding */ ClientToastWrapper),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ClientToastWrapper,useToast auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst ToastContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createContext(null);\nfunction ClientToastWrapper(param) {\n    let { children } = param;\n    _s();\n    const [toasts, setToasts] = react__WEBPACK_IMPORTED_MODULE_1___default().useState([]);\n    const toast = (props)=>{\n        const id = props.id || Math.random().toString(36).substr(2, 9);\n        // Add to toasts array\n        setToasts((prev)=>[\n                ...prev,\n                {\n                    id,\n                    props\n                }\n            ]);\n        // Auto-dismiss after duration\n        const duration = props.duration || 5000;\n        setTimeout(()=>{\n            dismiss(id);\n        }, duration);\n        return {\n            id,\n            dismiss: ()=>dismiss(id)\n        };\n    };\n    const dismiss = (toastId)=>{\n        if (toastId) {\n            setToasts((prev)=>prev.filter((toast)=>toast.id !== toastId));\n        } else {\n            setToasts([]);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: {\n            toast,\n            dismiss\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-4 right-4 z-50 space-y-2\",\n                children: toasts.map((param)=>{\n                    let { id, props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\\n              bg-white border rounded-lg shadow-lg p-4 min-w-[300px] max-w-[400px] relative\\n              \".concat(props.variant === 'destructive' ? 'border-red-200 bg-red-50' : '', \"\\n              \").concat(props.variant === 'success' ? 'border-green-200 bg-green-50' : '', \"\\n              \").concat(props.variant === 'warning' ? 'border-yellow-200 bg-yellow-50' : '', \"\\n            \"),\n                        children: [\n                            props.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-semibold mb-1\",\n                                children: props.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 29\n                            }, this),\n                            props.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: props.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 35\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>dismiss(id),\n                                className: \"absolute top-2 right-2 text-gray-400 hover:text-gray-600 w-6 h-6 flex items-center justify-center\",\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n_s(ClientToastWrapper, \"nD8TBOiFYf9ajstmZpZK2DP4rNo=\");\n_c = ClientToastWrapper;\n// Export the hook that components expect\nconst useToast = ()=>{\n    _s1();\n    const context = react__WEBPACK_IMPORTED_MODULE_1___default().useContext(ToastContext);\n    if (!context) {\n        console.error(\"useToast hook called outside of a ClientToastWrapper. Ensure ClientToastWrapper is placed correctly in your component tree (e.g., in client-providers.tsx).\");\n        throw new Error('useToast must be used within a ClientToastWrapper');\n    }\n    return context;\n};\n_s1(useToast, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ClientToastWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/providers/ClientToastWrapper.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/shadcn/alert.tsx":
/*!*****************************************!*\
  !*** ./src/components/shadcn/alert.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n// components/ui/alert.tsx\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\", {\n    variants: {\n        variant: {\n            default: \"bg-background text-foreground\",\n            destructive: \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Alert = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, variant, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\alert.tsx\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = Alert;\nAlert.displayName = \"Alert\";\nconst AlertTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c2 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"mb-1 font-medium leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\alert.tsx\",\n        lineNumber: 40,\n        columnNumber: 3\n    }, undefined);\n});\n_c3 = AlertTitle;\nAlertTitle.displayName = \"AlertTitle\";\nconst AlertDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c4 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\alert.tsx\",\n        lineNumber: 52,\n        columnNumber: 3\n    }, undefined);\n});\n_c5 = AlertDescription;\nAlertDescription.displayName = \"AlertDescription\";\n\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"Alert$React.forwardRef\");\n$RefreshReg$(_c1, \"Alert\");\n$RefreshReg$(_c2, \"AlertTitle$React.forwardRef\");\n$RefreshReg$(_c3, \"AlertTitle\");\n$RefreshReg$(_c4, \"AlertDescription$React.forwardRef\");\n$RefreshReg$(_c5, \"AlertDescription\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shadcn/alert.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/shadcn/button.tsx":
/*!******************************************!*\
  !*** ./src/components/shadcn/button.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n// components/shadcn/button.tsx\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-400 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-white\", {\n    variants: {\n        variant: {\n            default: \"bg-blue-600 text-white hover:bg-blue-700\",\n            destructive: \"bg-red-500 text-white hover:bg-red-600\",\n            outline: \"border border-gray-200 hover:bg-gray-100\",\n            secondary: \"bg-gray-200 text-gray-900 hover:bg-gray-300\",\n            ghost: \"hover:bg-gray-100\",\n            link: \"underline-offset-4 hover:underline text-blue-600\"\n        },\n        size: {\n            default: \"h-10 py-2 px-4\",\n            sm: \"h-9 px-3 rounded-md\",\n            lg: \"h-11 px-8 rounded-md\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, variant, size, asChild = false, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\button.tsx\",\n        lineNumber: 42,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = Button;\nButton.displayName = \"Button\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Button$React.forwardRef\");\n$RefreshReg$(_c1, \"Button\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shadcn/button.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/shadcn/card.tsx":
/*!****************************************!*\
  !*** ./src/components/shadcn/card.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n// components/ui/card.tsx\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\card.tsx\",\n        lineNumber: 10,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = Card;\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c2 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\card.tsx\",\n        lineNumber: 25,\n        columnNumber: 3\n    }, undefined);\n});\n_c3 = CardHeader;\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c4 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\card.tsx\",\n        lineNumber: 37,\n        columnNumber: 3\n    }, undefined);\n});\n_c5 = CardTitle;\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c6 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\card.tsx\",\n        lineNumber: 52,\n        columnNumber: 3\n    }, undefined);\n});\n_c7 = CardDescription;\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c8 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\card.tsx\",\n        lineNumber: 64,\n        columnNumber: 3\n    }, undefined);\n});\n_c9 = CardContent;\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c10 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\card.tsx\",\n        lineNumber: 72,\n        columnNumber: 3\n    }, undefined);\n});\n_c11 = CardFooter;\nCardFooter.displayName = \"CardFooter\";\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11;\n$RefreshReg$(_c, \"Card$React.forwardRef\");\n$RefreshReg$(_c1, \"Card\");\n$RefreshReg$(_c2, \"CardHeader$React.forwardRef\");\n$RefreshReg$(_c3, \"CardHeader\");\n$RefreshReg$(_c4, \"CardTitle$React.forwardRef\");\n$RefreshReg$(_c5, \"CardTitle\");\n$RefreshReg$(_c6, \"CardDescription$React.forwardRef\");\n$RefreshReg$(_c7, \"CardDescription\");\n$RefreshReg$(_c8, \"CardContent$React.forwardRef\");\n$RefreshReg$(_c9, \"CardContent\");\n$RefreshReg$(_c10, \"CardFooter$React.forwardRef\");\n$RefreshReg$(_c11, \"CardFooter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shadcn/card.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/shadcn/input.tsx":
/*!*****************************************!*\
  !*** ./src/components/shadcn/input.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n// components/ui/input.tsx\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, type, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-gray-200 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-400 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\input.tsx\",\n        lineNumber: 12,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = Input;\nInput.displayName = \"Input\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Input$React.forwardRef\");\n$RefreshReg$(_c1, \"Input\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shadcn/input.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/shadcn/progress.tsx":
/*!********************************************!*\
  !*** ./src/components/shadcn/progress.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Progress: () => (/* binding */ Progress),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Progress = (param)=>{\n    let { value = 0, className = \"\", indicatorClassName = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full h-2 bg-gray-200 rounded-full overflow-hidden \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full transition-all \".concat(indicatorClassName),\n            style: {\n                width: \"\".concat(value, \"%\")\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\progress.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\progress.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Progress;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Progress);\nvar _c;\n$RefreshReg$(_c, \"Progress\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3NoYWRjbi9wcm9ncmVzcy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTBCO0FBUW5CLE1BQU1DLFdBQW9DO1FBQUMsRUFBRUMsUUFBUSxDQUFDLEVBQUVDLFlBQVksRUFBRSxFQUFFQyxxQkFBcUIsRUFBRSxFQUFFO0lBQ3RHLHFCQUNFLDhEQUFDQztRQUFJRixXQUFXLGdFQUEwRSxPQUFWQTtrQkFDOUUsNEVBQUNFO1lBQ0NGLFdBQVcseUJBQTRDLE9BQW5CQztZQUNwQ0UsT0FBTztnQkFBRUMsT0FBTyxHQUFTLE9BQU5MLE9BQU07WUFBRzs7Ozs7Ozs7Ozs7QUFJcEMsRUFBRTtLQVRXRDtBQVdiLGlFQUFlQSxRQUFRQSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHBjXFxPbmVEcml2ZVxcRGVza3RvcFxcRGVza3RvcFxcU29seW50YV9XZWJzaXRlXFxmcm9udGVuZFxcbGVzc29uLXBsYXRmb3JtXFxzcmNcXGNvbXBvbmVudHNcXHNoYWRjblxccHJvZ3Jlc3MudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XHJcblxyXG5pbnRlcmZhY2UgUHJvZ3Jlc3NQcm9wcyB7XHJcbiAgdmFsdWU/OiBudW1iZXI7XHJcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xyXG4gIGluZGljYXRvckNsYXNzTmFtZT86IHN0cmluZztcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IFByb2dyZXNzOiBSZWFjdC5GQzxQcm9ncmVzc1Byb3BzPiA9ICh7IHZhbHVlID0gMCwgY2xhc3NOYW1lID0gXCJcIiwgaW5kaWNhdG9yQ2xhc3NOYW1lID0gXCJcIiB9KSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPXtgcmVsYXRpdmUgdy1mdWxsIGgtMiBiZy1ncmF5LTIwMCByb3VuZGVkLWZ1bGwgb3ZlcmZsb3ctaGlkZGVuICR7Y2xhc3NOYW1lfWB9PlxyXG4gICAgICA8ZGl2XHJcbiAgICAgICAgY2xhc3NOYW1lPXtgaC1mdWxsIHRyYW5zaXRpb24tYWxsICR7aW5kaWNhdG9yQ2xhc3NOYW1lfWB9XHJcbiAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGAke3ZhbHVlfSVgIH19XHJcbiAgICAgIC8+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgUHJvZ3Jlc3M7Il0sIm5hbWVzIjpbIlJlYWN0IiwiUHJvZ3Jlc3MiLCJ2YWx1ZSIsImNsYXNzTmFtZSIsImluZGljYXRvckNsYXNzTmFtZSIsImRpdiIsInN0eWxlIiwid2lkdGgiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shadcn/progress.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/ErrorDisplay.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/ErrorDisplay.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ErrorDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Info!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Info!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/info.js\");\n\n\n\nfunction ErrorDisplay(param) {\n    let { title, message, actionText, onAction, type = 'error' } = param;\n    const getIcon = ()=>{\n        switch(type){\n            case 'warning':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"h-12 w-12 text-amber-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ui\\\\ErrorDisplay.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 16\n                }, this);\n            case 'info':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-12 w-12 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ui\\\\ErrorDisplay.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"h-12 w-12 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ui\\\\ErrorDisplay.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getColorClasses = ()=>{\n        switch(type){\n            case 'warning':\n                return 'bg-amber-50 border-amber-200';\n            case 'info':\n                return 'bg-blue-50 border-blue-200';\n            case 'error':\n            default:\n                return 'bg-red-50 border-red-200';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"rounded-lg border p-6 \".concat(getColorClasses(), \" flex flex-col items-center text-center\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: getIcon()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ui\\\\ErrorDisplay.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-xl font-semibold mb-2\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ui\\\\ErrorDisplay.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600 mb-6\",\n                children: message\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ui\\\\ErrorDisplay.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            actionText && onAction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onAction,\n                className: \"px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors\",\n                children: actionText\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ui\\\\ErrorDisplay.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ui\\\\ErrorDisplay.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n_c = ErrorDisplay;\nvar _c;\n$RefreshReg$(_c, \"ErrorDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/ErrorDisplay.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/LoadingSpinner.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/LoadingSpinner.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoadingSpinner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction LoadingSpinner(param) {\n    let { size = 'medium' } = param;\n    const sizeClasses = {\n        small: 'w-4 h-4 border-2',\n        medium: 'w-8 h-8 border-3',\n        large: 'w-12 h-12 border-4'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-center items-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(sizeClasses[size], \" border-gray-300 border-t-primary rounded-full animate-spin\")\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n_c = LoadingSpinner;\nvar _c;\n$RefreshReg$(_c, \"LoadingSpinner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL0xvYWRpbmdTcGlubmVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUUwQjtBQU1YLFNBQVNDLGVBQWUsS0FBd0M7UUFBeEMsRUFBRUMsT0FBTyxRQUFRLEVBQXVCLEdBQXhDO0lBQ3JDLE1BQU1DLGNBQWM7UUFDbEJDLE9BQU87UUFDUEMsUUFBUTtRQUNSQyxPQUFPO0lBQ1Q7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVyxHQUFxQixPQUFsQkwsV0FBVyxDQUFDRCxLQUFLLEVBQUM7Ozs7Ozs7Ozs7O0FBRzNDO0tBWndCRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwY1xcT25lRHJpdmVcXERlc2t0b3BcXERlc2t0b3BcXFNvbHludGFfV2Vic2l0ZVxcZnJvbnRlbmRcXGxlc3Nvbi1wbGF0Zm9ybVxcc3JjXFxjb21wb25lbnRzXFx1aVxcTG9hZGluZ1NwaW5uZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XHJcblxyXG5pbnRlcmZhY2UgTG9hZGluZ1NwaW5uZXJQcm9wcyB7XHJcbiAgc2l6ZT86ICdzbWFsbCcgfCAnbWVkaXVtJyB8ICdsYXJnZSc7XHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmdTcGlubmVyKHsgc2l6ZSA9ICdtZWRpdW0nIH06IExvYWRpbmdTcGlubmVyUHJvcHMpIHtcclxuICBjb25zdCBzaXplQ2xhc3NlcyA9IHtcclxuICAgIHNtYWxsOiAndy00IGgtNCBib3JkZXItMicsXHJcbiAgICBtZWRpdW06ICd3LTggaC04IGJvcmRlci0zJyxcclxuICAgIGxhcmdlOiAndy0xMiBoLTEyIGJvcmRlci00J1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtgJHtzaXplQ2xhc3Nlc1tzaXplXX0gYm9yZGVyLWdyYXktMzAwIGJvcmRlci10LXByaW1hcnkgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpbmB9PjwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufSJdLCJuYW1lcyI6WyJSZWFjdCIsIkxvYWRpbmdTcGlubmVyIiwic2l6ZSIsInNpemVDbGFzc2VzIiwic21hbGwiLCJtZWRpdW0iLCJsYXJnZSIsImRpdiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/LoadingSpinner.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n/**\r\n * A simple Badge component.\r\n */ const Badge = (param)=>{\n    let { variant = 'default', className = '', children, ...props } = param;\n    let variantClasses = '';\n    switch(variant){\n        case 'destructive':\n            variantClasses = 'bg-red-500 text-white';\n            break;\n        case 'secondary':\n            variantClasses = 'bg-gray-300 text-gray-900';\n            break;\n        case 'info':\n            variantClasses = 'bg-blue-500 text-white';\n            break;\n        case 'success':\n            variantClasses = 'bg-green-500 text-white';\n            break;\n        case 'warning':\n            variantClasses = 'bg-yellow-500 text-gray-800';\n            break;\n        case 'default':\n        default:\n            variantClasses = 'bg-gray-200 text-gray-800';\n            break;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"inline-flex items-center rounded-full px-2 py-1 text-xs font-medium \".concat(variantClasses, \" \").concat(className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Badge;\nvar _c;\n$RefreshReg$(_c, \"Badge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/badge.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useSessionSimple.tsx":
/*!****************************************!*\
  !*** ./src/hooks/useSessionSimple.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionContext: () => (/* binding */ SessionContext),\n/* harmony export */   useSession: () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/authService */ \"(app-pages-browser)/./src/lib/authService.ts\");\n/* __next_internal_client_entry_do_not_use__ useSession,SessionContext auto */ var _s = $RefreshSig$();\n\n // Import real auth service\n// Simplified session context for the frontend diagnostic fix\nconst SessionContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\nfunction useSession() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(SessionContext);\n    if (context === undefined) {\n        // Instead of throwing an error immediately, return a fallback object\n        console.warn('useSession called outside of SessionProvider, returning fallback values');\n        return {\n            backendSessionId: null,\n            user: null,\n            setUserSession: ()=>{},\n            setBackendSessionId: ()=>{},\n            clearSession: ()=>{},\n            isReady: false,\n            isLoading: true,\n            getAuthHeaders: ()=>(0,_lib_authService__WEBPACK_IMPORTED_MODULE_1__.getAuthHeaders)(null),\n            userRole: null\n        };\n    }\n    return context;\n}\n_s(useSession, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Export the context for providers to use\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSessionSimple.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/authService.ts":
/*!********************************!*\
  !*** ./src/lib/authService.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CURRENT_SESSION_KEY: () => (/* binding */ CURRENT_SESSION_KEY),\n/* harmony export */   clearAuthData: () => (/* binding */ clearAuthData),\n/* harmony export */   findUserByUserId: () => (/* binding */ findUserByUserId),\n/* harmony export */   getAuthHeaders: () => (/* binding */ getAuthHeaders),\n/* harmony export */   getFreshAuthHeaders: () => (/* binding */ getFreshAuthHeaders),\n/* harmony export */   getUserRole: () => (/* binding */ getUserRole),\n/* harmony export */   getUserSession: () => (/* binding */ getUserSession),\n/* harmony export */   refreshAuthToken: () => (/* binding */ refreshAuthToken),\n/* harmony export */   saveUserSession: () => (/* binding */ saveUserSession),\n/* harmony export */   setupAuthListener: () => (/* binding */ setupAuthListener),\n/* harmony export */   setupAuthStateListener: () => (/* binding */ setupAuthStateListener),\n/* harmony export */   signInWithEmailAndPassword: () => (/* binding */ signInWithEmailAndPassword),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   syncAuthState: () => (/* binding */ syncAuthState)\n/* harmony export */ });\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n// lib/authService.ts\n/* __next_internal_client_entry_do_not_use__ CURRENT_SESSION_KEY,saveUserSession,getUserSession,clearAuthData,signInWithEmailAndPassword,signOut,setupAuthStateListener,setupAuthListener,getAuthHeaders,getFreshAuthHeaders,refreshAuthToken,findUserByUserId,getUserRole,syncAuthState auto */ \n\n\n// Constants\nconst SESSION_KEY = 'user_session';\nconst CURRENT_SESSION_KEY = 'current_session'; // Export this constant\nconst TOKEN_KEY = 'token';\n/**\r\n * Save user session to localStorage with consistent keys\r\n */ const saveUserSession = (session)=>{\n    if (!session || !session.uid) return;\n    try {\n        // Add timestamp before saving\n        const sessionToSave = {\n            ...session,\n            tokenTimestamp: Date.now()\n        };\n        // Save the full session object with timestamp\n        localStorage.setItem(SESSION_KEY, JSON.stringify(sessionToSave));\n        // CURRENT_SESSION_KEY should be set explicitly elsewhere when the *backend* session ID is known.\n        // Do not automatically set it to the Firebase UID here.\n        // localStorage.setItem(CURRENT_SESSION_KEY, session.uid); // Removed this line\n        localStorage.setItem(TOKEN_KEY, session.token); // Keep saving the token\n        console.log('Session object saved for UID:', session.uid);\n    } catch (error) {\n        console.error('Error saving user session:', error);\n    }\n};\n/**\r\n * Get the current user session from localStorage\r\n */ const getUserSession = ()=>{\n    try {\n        const sessionStr = localStorage.getItem(SESSION_KEY);\n        if (!sessionStr) return null;\n        return JSON.parse(sessionStr);\n    } catch (error) {\n        console.error('Failed to parse user session:', error);\n        return null;\n    }\n};\n/**\r\n * Clear all auth-related data from localStorage\r\n */ const clearAuthData = ()=>{\n    try {\n        localStorage.removeItem(SESSION_KEY);\n        localStorage.removeItem(TOKEN_KEY);\n        localStorage.removeItem(CURRENT_SESSION_KEY);\n        localStorage.removeItem('authMethod');\n        localStorage.removeItem('viewing_as_child');\n        localStorage.removeItem('parent_id');\n        localStorage.removeItem('parent_name');\n        // Remove additional role and session-related keys that may linger\n        localStorage.removeItem('user_role');\n        localStorage.removeItem('role');\n        localStorage.removeItem('calendly-store'); // In case our app inadvertently wrote it\n        localStorage.removeItem('calendly-internal-store');\n        localStorage.removeItem('user_name');\n        localStorage.removeItem('parentEnrollmentMessage');\n    } catch (error) {\n        console.error('Error clearing auth data:', error);\n    }\n};\n/**\r\n * Sign in with email and password\r\n */ const signInWithEmailAndPassword = async (email, password)=>{\n    // Clear any previous auth state first\n    await signOut();\n    console.log(\"Attempting email/password sign-in\");\n    try {\n        var _auth_currentUser;\n        // Use Firebase's email/password auth\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signInWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, email, password);\n        const user = userCredential.user;\n        // Get fresh token with custom claims\n        const additionalClaims = {\n            student_id: localStorage.getItem('viewing_as_child') || undefined\n        };\n        const tokenResult = await user.getIdTokenResult(true);\n        const token = await ((_auth_currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser) === null || _auth_currentUser === void 0 ? void 0 : _auth_currentUser.getIdToken(true, additionalClaims));\n        // Get user details from Firestore\n        const userDetails = await getUserDetailsFromFirestore(user);\n        // Create session\n        const userSession = {\n            uid: user.uid,\n            email: user.email,\n            name: user.displayName || (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || null,\n            token: token,\n            role: userDetails === null || userDetails === void 0 ? void 0 : userDetails.role\n        };\n        // Save session\n        saveUserSession(userSession);\n        console.log(\"Authentication successful\");\n        return userSession;\n    } catch (error) {\n        console.error(\"Authentication error:\", error);\n        throw error;\n    }\n};\n/**\r\n * Sign out the current user\r\n */ const signOut = async ()=>{\n    try {\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signOut)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth);\n        clearAuthData();\n        console.log(\"User signed out\");\n    } catch (error) {\n        console.error(\"Sign out error:\", error);\n        throw error;\n    }\n};\n/**\r\n * Set up a listener for auth state changes\r\n */ const setupAuthStateListener = (callback)=>{\n    return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.onAuthStateChanged)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, callback);\n};\n/**\r\n * Setup auth listener used by the session provider\r\n * This matches the signature expected by useSession\r\n */ const setupAuthListener = (setSession, setError, setIsLoading)=>{\n    return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.onAuthStateChanged)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, async (user)=>{\n        console.log(\"Auth state changed:\", user ? \"User \".concat(user.uid) : \"No user\");\n        if (!user) {\n            setSession(null);\n            setIsLoading(false);\n            return;\n        }\n        try {\n            var _auth_currentUser;\n            // Get fresh token for signed-in user\n            // Get custom claims including student_id if viewing as parent\n            const additionalClaims = {\n                student_id: localStorage.getItem('viewing_as_child') || undefined\n            };\n            const tokenResult = await user.getIdTokenResult(true);\n            const tokenString = await ((_auth_currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser) === null || _auth_currentUser === void 0 ? void 0 : _auth_currentUser.getIdToken(true, additionalClaims));\n            if (!tokenString) {\n                throw new Error('Failed to get authentication token');\n            }\n            // Get user details from Firestore\n            const userDetails = await getUserDetailsFromFirestore(user);\n            // Create session object with token string\n            const userSession = {\n                uid: user.uid,\n                email: user.email || '',\n                name: user.displayName || (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || '',\n                token: tokenString,\n                tokenResult,\n                role: userDetails === null || userDetails === void 0 ? void 0 : userDetails.role\n            };\n            // Set session and store backend session ID if available\n            setSession(userSession);\n            // If this is a new login response with sessionId, store it\n            const responseSessionId = user.sessionId;\n            if (responseSessionId) {\n                localStorage.setItem(CURRENT_SESSION_KEY, responseSessionId);\n            }\n        } catch (error) {\n            console.error(\"Error getting auth token:\", error);\n            setError(\"Failed to authenticate session\");\n            setSession(null);\n        } finally{\n            setIsLoading(false);\n        }\n    });\n};\n/**\r\n * Get auth headers for API requests\r\n * Accepts backendSessionId from context to avoid localStorage race conditions.\r\n */ const getAuthHeaders = (backendSessionIdFromContext)=>{\n    const headers = {\n        'Content-Type': 'application/json'\n    };\n    const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n    let currentToken = null;\n    let currentUid = null;\n    if (currentUser) {\n        var _currentUser_stsTokenManager;\n        currentUid = currentUser.uid;\n        // Attempt to get token from Firebase auth state first\n        currentToken = ((_currentUser_stsTokenManager = currentUser.stsTokenManager) === null || _currentUser_stsTokenManager === void 0 ? void 0 : _currentUser_stsTokenManager.accessToken) || null;\n    }\n    const storedSession = getUserSession();\n    // Prefer the ID passed from context, fall back to localStorage only if necessary (e.g., during initial load before context is ready)\n    const effectiveBackendSessionId = backendSessionIdFromContext || localStorage.getItem(CURRENT_SESSION_KEY);\n    // Use the effective backend session ID for the Session-ID header\n    if (effectiveBackendSessionId) {\n        headers['Session-ID'] = effectiveBackendSessionId;\n    } else {\n        // Fallback to UID only if backend session ID isn't available anywhere\n        const effectiveUid = currentUid || (storedSession === null || storedSession === void 0 ? void 0 : storedSession.uid);\n        if (effectiveUid) {\n            console.warn(\"Using UID (\".concat(effectiveUid, \") as Session-ID header fallback. Backend session ID not found in context or localStorage ('\").concat(CURRENT_SESSION_KEY, \"').\"));\n            headers['Session-ID'] = effectiveUid; // Still might be wrong, but it's the last resort\n        } else {\n            console.error(\"Cannot set Session-ID header: No backend session ID or user UID found.\");\n        }\n    }\n    // Prefer token from context's stored session if Firebase token is missing\n    const effectiveToken = currentToken || (storedSession === null || storedSession === void 0 ? void 0 : storedSession.token);\n    if (effectiveToken) {\n        headers['Authorization'] = \"Bearer \".concat(effectiveToken);\n    } else {\n        console.warn(\"Authorization token not found in Firebase state or stored session. This may cause authentication errors.\");\n        // Instead of completely failing, let's try to get token from localStorage as last resort\n        const fallbackToken = localStorage.getItem('token');\n        if (fallbackToken) {\n            console.warn(\"Using fallback token from localStorage\");\n            headers['Authorization'] = \"Bearer \".concat(fallbackToken);\n        } else {\n            console.error(\"No authentication token available from any source.\");\n        }\n    }\n    // Get role from stored session if available\n    const effectiveRole = (storedSession === null || storedSession === void 0 ? void 0 : storedSession.role) || 'student'; // Default role if not found\n    headers['X-User-Role'] = effectiveRole;\n    // CRITICAL FIX: Add testing mode header when no valid authentication is available\n    if (!effectiveToken && !effectiveBackendSessionId) {\n        console.warn(\"No authentication available, setting testing mode header\");\n        headers['X-Testing-Mode'] = 'true';\n        // For testing purposes, set a default student ID if none available\n        if (!headers['Session-ID']) {\n            headers['Session-ID'] = 'andrea_ugono_33305'; // Default test student ID\n        }\n    }\n    console.log(\"Generated auth headers:\", {\n        hasAuth: !!headers['Authorization'],\n        hasSessionId: !!headers['Session-ID'],\n        role: headers['X-User-Role'],\n        testingMode: headers['X-Testing-Mode']\n    });\n    return headers;\n    // This allows backend to generate console logs for lesson interactions during development\n    if (!effectiveToken || effectiveToken === 'undefined' || effectiveToken === 'null') {\n        console.warn(\"No valid authentication token found - enabling testing mode for backend logging\");\n        headers['X-Testing-Mode'] = 'true';\n    }\n    return headers;\n};\n/**\r\n * Get fresh auth headers with token refresh\r\n * Accepts backendSessionId from context.\r\n */ const getFreshAuthHeaders = async (backendSessionIdFromContext)=>{\n    const headers = {\n        'Content-Type': 'application/json'\n    };\n    const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n    if (currentUser) {\n        try {\n            const token = await currentUser.getIdToken(true); // Force refresh\n            headers['Authorization'] = \"Bearer \".concat(token);\n            // Use the effective backend session ID for the Session-ID header\n            const effectiveBackendSessionId = backendSessionIdFromContext || localStorage.getItem(CURRENT_SESSION_KEY);\n            if (effectiveBackendSessionId) {\n                headers['Session-ID'] = effectiveBackendSessionId;\n            } else {\n                // Fallback to UID only if backend session ID isn't available anywhere\n                console.warn(\"Using UID (\".concat(currentUser.uid, \") as Session-ID header fallback during fresh token request. Backend session ID not found.\"));\n                headers['Session-ID'] = currentUser.uid; // Last resort\n            }\n            const storedSession = getUserSession();\n            headers['X-User-Role'] = (storedSession === null || storedSession === void 0 ? void 0 : storedSession.role) || 'student'; // Default role\n        } catch (error) {\n            console.error(\"Error getting fresh token:\", error);\n            // Fallback to non-fresh headers if refresh fails\n            return getAuthHeaders(backendSessionIdFromContext);\n        }\n    } else {\n        // If no current user, return standard (likely unauthenticated) headers with testing mode\n        const fallbackHeaders = getAuthHeaders(backendSessionIdFromContext);\n        fallbackHeaders['X-Testing-Mode'] = 'true';\n        console.warn(\"No current user found - enabling testing mode for backend logging\");\n        return fallbackHeaders;\n    }\n    return headers;\n};\n/**\r\n * Refresh the auth token\r\n */ const refreshAuthToken = async ()=>{\n    try {\n        const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n        if (!currentUser) {\n            console.error(\"No current user found for token refresh\");\n            return null;\n        }\n        // Force refresh the token\n        const newToken = await currentUser.getIdToken(true);\n        // Update the stored session with new token\n        const storedSession = getUserSession();\n        if (storedSession) {\n            const updatedSession = {\n                ...storedSession,\n                token: newToken\n            };\n            saveUserSession(updatedSession);\n        }\n        return newToken;\n    } catch (error) {\n        console.error(\"Failed to refresh authentication token:\", error);\n        return null;\n    }\n};\n/**\r\n * Get user details from Firestore\r\n */ async function getUserDetailsFromFirestore(user) {\n    try {\n        const db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'users', user.uid);\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(userRef);\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            return {\n                name: data.name,\n                role: data.role,\n                children: data.children || [],\n                parents: data.parents || []\n            };\n        }\n        // Fallback to check parents collection if not found in users\n        const parentRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'parents', user.uid);\n        const parentDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(parentRef);\n        if (parentDoc.exists()) {\n            const data = parentDoc.data();\n            return {\n                name: data.name,\n                role: 'parent',\n                children: data.children || []\n            };\n        }\n        // Fallback to check students collection\n        const studentRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'students', user.uid);\n        const studentDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(studentRef);\n        if (studentDoc.exists()) {\n            const data = studentDoc.data();\n            return {\n                name: data.name,\n                role: 'student',\n                parents: data.parents || []\n            };\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error fetching user details:\", error);\n        return null;\n    }\n}\n/**\r\n * Find user by userId (for child accounts)\r\n */ const findUserByUserId = async (userId)=>{\n    try {\n        const db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)();\n        const usersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(db, 'users');\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)(usersRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('userId', '==', userId));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        if (querySnapshot.empty) {\n            return null;\n        }\n        return querySnapshot.docs[0].data().email;\n    } catch (error) {\n        console.error(\"Error finding user by userId:\", error);\n        return null;\n    }\n};\n/**\r\n * Get user role from Firestore\r\n */ const getUserRole = async (uid)=>{\n    try {\n        const db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'users', uid);\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(userRef);\n        if (userDoc.exists() && userDoc.data().role) {\n            return userDoc.data().role;\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error getting user role:\", error);\n        return null;\n    }\n};\n/**\r\n * Sync Firebase auth state with local storage\r\n * This is crucial to fix the state mismatch issues\r\n */ const syncAuthState = async ()=>{\n    const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n    const storedSession = getUserSession();\n    // Case 1: Firebase has user but localStorage doesn't\n    if (currentUser && (!storedSession || storedSession.uid !== currentUser.uid)) {\n        console.log(\"Syncing: Firebase has user but localStorage doesn't match\");\n        const token = await currentUser.getIdToken(true);\n        const userDetails = await getUserDetailsFromFirestore(currentUser);\n        const userSession = {\n            uid: currentUser.uid,\n            email: currentUser.email,\n            name: currentUser.displayName || (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || null,\n            token: token,\n            role: userDetails === null || userDetails === void 0 ? void 0 : userDetails.role\n        };\n        saveUserSession(userSession);\n        return userSession;\n    }\n    // Case 2: Firebase has no user but localStorage does\n    if (!currentUser && storedSession) {\n        console.log(\"Syncing: Firebase has no user but localStorage does\");\n        clearAuthData();\n        return null;\n    }\n    // Case 3: Both have matching user, check if token needs refresh\n    if (currentUser && storedSession && currentUser.uid === storedSession.uid) {\n        console.log(\"Syncing: Both have matching user\");\n        // Token is older than 30 minutes, refresh it\n        const tokenDate = new Date(storedSession.tokenTimestamp || 0);\n        const now = new Date();\n        const diffMinutes = (now.getTime() - tokenDate.getTime()) / (1000 * 60);\n        if (diffMinutes > 30) {\n            console.log(\"Token is older than 30 minutes, refreshing\");\n            await refreshAuthToken();\n        }\n    }\n    return storedSession;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/authService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   app: () => (/* binding */ app),\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(app-pages-browser)/./node_modules/firebase/app/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/process/browser.js\");\n// lib/firebase.ts\n/* __next_internal_client_entry_do_not_use__ app,auth,db,storage auto */ \n\n\n\n// Default Firebase configuration for development\nconst devConfig = {\n    apiKey: \"AIzaSyDWVM8PvcWD4nAkpsI7FuDKCvpp_PEnPlU\",\n    authDomain: \"solynta-academy.firebaseapp.com\",\n    projectId: \"solynta-academy\",\n    storageBucket: \"solynta-academy.firebasestorage.app\",\n    messagingSenderId: \"914922463191\",\n    appId: \"1:914922463191:web:b6e9c737dba77a26643592\",\n    measurementId: \"G-ZVC7R06Y33\"\n};\n// Firebase configuration - try environment variables first, then fallback to dev config\nconst firebaseConfig = {\n    apiKey: \"AIzaSyDWVM8PvcWD4nAkpsI7FuDKCvpp_PEnPlU\" || 0,\n    authDomain: \"solynta-academy.firebaseapp.com\" || 0,\n    projectId: \"solynta-academy\" || 0,\n    storageBucket: \"solynta-academy.firebasestorage.app\" || 0,\n    messagingSenderId: \"914922463191\" || 0,\n    appId: \"1:914922463191:web:b6e9c737dba77a26643592\" || 0,\n    measurementId: \"G-ZVC7R06Y33\" || 0\n};\nconsole.log('Using Firebase config with project ID:', firebaseConfig.projectId);\n// Initialize Firebase app (Singleton pattern)\nconst app = !(0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length ? (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig) : (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApp)();\n// Initialize services - these will be initialized client-side\nlet auth;\nlet db;\nlet storage;\n// Check if running in a browser environment\nif (true) {\n    // Initialize Auth\n    auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\n    (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.setPersistence)(auth, firebase_auth__WEBPACK_IMPORTED_MODULE_1__.browserLocalPersistence).catch((error)=>console.error(\"Auth persistence error:\", error));\n    // Initialize Firestore with error handling\n    try {\n        db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.initializeFirestore)(app, {\n            cacheSizeBytes: firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.CACHE_SIZE_UNLIMITED,\n            experimentalForceLongPolling: true,\n            ignoreUndefinedProperties: true\n        });\n        console.log('Firestore initialized successfully');\n    } catch (error) {\n        console.error('Error initializing Firestore:', error);\n        // Fallback to default Firestore initialization\n        try {\n            const { getFirestore } = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/index.cjs.js\");\n            db = getFirestore(app);\n            console.log('Firestore initialized with fallback method');\n        } catch (fallbackError) {\n            console.error('Firestore fallback initialization failed:', fallbackError);\n        }\n    }\n    // Initialize Storage\n    storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\n    // Connect to emulators in development if configured\n    if ( true && process.env.NEXT_PUBLIC_USE_FIREBASE_EMULATOR === 'true') {\n        console.log(\"Connecting to Firebase Emulators...\");\n        // Use dynamic imports for emulator functions to potentially reduce bundle size\n        Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\")).then((param)=>{\n            let { connectAuthEmulator } = param;\n            try {\n                connectAuthEmulator(auth, 'http://localhost:9099', {\n                    disableWarnings: true\n                });\n                console.log(\"Auth Emulator connected to http://localhost:9099\");\n            } catch (error) {\n                console.error(\"Error connecting to Auth Emulator:\", error);\n            }\n        });\n        Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\")).then((param)=>{\n            let { connectFirestoreEmulator } = param;\n            try {\n                connectFirestoreEmulator(db, 'localhost', 8080);\n                console.log(\"Firestore Emulator connected to localhost:8080\");\n            } catch (error) {\n                console.error(\"Error connecting to Firestore Emulator:\", error);\n            }\n        });\n        Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\")).then((param)=>{\n            let { connectStorageEmulator } = param;\n            try {\n                connectStorageEmulator(storage, 'localhost', 9199);\n                console.log(\"Storage Emulator connected to localhost:9199\");\n            } catch (error) {\n                console.error(\"Error connecting to Storage Emulator:\", error);\n            }\n        });\n    }\n} else {}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/firebase.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   extractContextFromRequest: () => (/* binding */ extractContextFromRequest),\n/* harmony export */   fetchWithErrorHandling: () => (/* binding */ fetchWithErrorHandling),\n/* harmony export */   formatGradeLevelForDisplay: () => (/* binding */ formatGradeLevelForDisplay),\n/* harmony export */   getParentId: () => (/* binding */ getParentId),\n/* harmony export */   mapFrontendToBackendFields: () => (/* binding */ mapFrontendToBackendFields),\n/* harmony export */   normalizeGradeLevel: () => (/* binding */ normalizeGradeLevel),\n/* harmony export */   prepareBackendRequest: () => (/* binding */ prepareBackendRequest),\n/* harmony export */   returnToParentAccount: () => (/* binding */ returnToParentAccount),\n/* harmony export */   switchToChildAccount: () => (/* binding */ switchToChildAccount)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\r\n * Utility function to combine Tailwind CSS class names intelligently.\r\n * Handles merging and conflict resolution.\r\n * @param inputs Class values to combine.\r\n * @returns Merged class string.\r\n */ function cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\r\n * Formats a grade level string for display.\r\n * Handles formats like \"primary-5\", \"jss1\", \"SSS2\", \"jss-1-3\", etc.\r\n * @param gradeLevelInput Raw grade level string (e.g., \"primary-5\", \"jss1\")\r\n * @returns Formatted string (e.g., \"Primary 5\", \"Junior Secondary School 1\", \"Junior Secondary School 1-3\") or the original input if no specific format matches.\r\n */ function formatGradeLevelForDisplay(gradeLevelInput) {\n    if (!gradeLevelInput) {\n        return 'N/A'; // Or return empty string ''\n    }\n    const input = gradeLevelInput.trim().toLowerCase();\n    // Handle \"primary-X\" format\n    if (input.startsWith('primary-')) {\n        const parts = input.split('-');\n        if (parts.length === 2 && !isNaN(parseInt(parts[1], 10))) {\n            return \"Primary \".concat(parts[1]);\n        }\n    }\n    // Handle specific range case \"jss-1-3\" first\n    if (input === 'jss-1-3') {\n        return 'Junior Secondary School 1-3';\n    }\n    // Handle \"jssX\" or \"jss-X\" format (single number)\n    if (input.startsWith('jss')) {\n        const numPart = input.replace('jss', '').replace('-', '').trim();\n        if (!isNaN(parseInt(numPart, 10))) {\n            return \"Junior Secondary School \".concat(numPart);\n        }\n    }\n    // Handle specific range case \"sss-1-3\" first\n    if (input === 'sss-1-3') {\n        return 'Senior Secondary School 1-3';\n    }\n    // Handle \"sssX\" or \"sss-X\" format (single number)\n    if (input.startsWith('sss')) {\n        const numPart = input.replace('sss', '').replace('-', '').trim();\n        if (!isNaN(parseInt(numPart, 10))) {\n            return \"Senior Secondary School \".concat(numPart);\n        }\n    }\n    // Add more specific rules if needed (e.g., \"P5\" -> \"Primary 5\")\n    if (input === 'p5') {\n        return 'Primary 5';\n    }\n    // Add more rules like the one above for P1, P2, etc. if needed\n    // Fallback: Return the original input string if no rules matched\n    // Consider capitalizing if appropriate as a fallback\n    // return gradeLevelInput.charAt(0).toUpperCase() + gradeLevelInput.slice(1);\n    return gradeLevelInput;\n}\n/**\r\n * Normalizes various grade level input strings to a consistent internal format.\r\n * Example: \"Primary 5\" -> \"primary-5\", \"Junior Secondary School 1\" -> \"jss1\"\r\n * @param gradeLevelInput Raw or display grade level string.\r\n * @returns Normalized string (lowercase, specific format) or original lowercase if no rule matches.\r\n */ function normalizeGradeLevel(gradeLevelInput) {\n    if (!gradeLevelInput) return '';\n    const input = gradeLevelInput.trim().toLowerCase();\n    // Normalization rules\n    if (input.startsWith('primary')) {\n        const numPart = input.replace('primary', '').trim();\n        if (!isNaN(parseInt(numPart, 10))) {\n            return \"primary-\".concat(numPart); // \"primary 5\" -> \"primary-5\"\n        }\n    }\n    if (input.startsWith('junior secondary school')) {\n        // Handle range first\n        if (input === 'junior secondary school 1-3') return 'jss-1-3';\n        const numPart = input.replace('junior secondary school', '').trim();\n        if (!isNaN(parseInt(numPart, 10))) {\n            return \"jss\".concat(numPart); // \"Junior Secondary School 1\" -> \"jss1\"\n        }\n    }\n    if (input.startsWith('senior secondary school')) {\n        // Handle range first\n        if (input === 'senior secondary school 1-3') return 'sss-1-3';\n        const numPart = input.replace('senior secondary school', '').trim();\n        if (!isNaN(parseInt(numPart, 10))) {\n            return \"sss\".concat(numPart); // \"Senior Secondary School 2\" -> \"sss2\"\n        }\n    }\n    // Add more normalization rules as needed (e.g., p5 -> primary-5)\n    if (input === 'p5') return 'primary-5';\n    // Add rules for p1, p2, etc.\n    // Return input directly if it already matches a normalized format or no rule applies\n    return input;\n}\n// lib/api-utils.ts\n/**\r\n * Helper function to fetch data with built-in error handling\r\n */ async function fetchWithErrorHandling(url, options) {\n    try {\n        console.log(\"Fetching: \".concat(url));\n        const response = await fetch(url, options);\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(\"API Error (\".concat(response.status, \"):\"), errorText);\n            throw new Error(\"API request failed with status \".concat(response.status, \": \").concat(response.statusText));\n        }\n        const data = await response.json();\n        console.log('API Response:', data);\n        return data;\n    } catch (error) {\n        console.error('Fetch error:', error);\n        throw error;\n    }\n}\n/**\r\n * Helper to get parent ID from localStorage\r\n */ function getParentId() {\n    return localStorage.getItem('parent_id') || localStorage.getItem('current_session');\n}\n/**\r\n * Helper to switch to a child's account (for parent viewing)\r\n */ function switchToChildAccount(childId, parentId) {\n    // Store the child's session ID in localStorage\n    localStorage.setItem('current_session', childId);\n    // Set parent ID for returning back\n    localStorage.setItem('parent_id', parentId);\n    // Set flag to indicate parent is viewing child's dashboard\n    localStorage.setItem('viewing_as_child', 'true');\n    // Store a message to display on the child's dashboard\n    localStorage.setItem('parentEnrollmentMessage', \"You're viewing your child's dashboard. All actions will be performed on their behalf.\");\n}\n/**\r\n * Helper to switch back to parent account\r\n */ function returnToParentAccount() {\n    const parentId = localStorage.getItem('parent_id');\n    if (parentId) {\n        // Clear the viewing_as_child flag\n        localStorage.removeItem('viewing_as_child');\n        // Set the parent ID as the current session\n        localStorage.setItem('current_session', parentId);\n        // Clear the parent message\n        localStorage.removeItem('parentEnrollmentMessage');\n    }\n}\n/**\r\n * Extracts context parameters from a request, including URL parameters and headers.\r\n * Used to properly pass required data to backend APIs.\r\n */ function extractContextFromRequest(request) {\n    const url = new URL(request.url);\n    return {\n        studentId: request.headers.get('X-Student-ID') || url.searchParams.get('studentId'),\n        country: request.headers.get('X-Country') || url.searchParams.get('country'),\n        curriculum: request.headers.get('X-Curriculum') || url.searchParams.get('curriculum'),\n        grade: request.headers.get('X-Grade') || url.searchParams.get('grade'),\n        level: request.headers.get('X-Level') || url.searchParams.get('level'),\n        subject: request.headers.get('X-Subject') || url.searchParams.get('subject')\n    };\n}\n/**\r\n * Maps frontend field names to backend field names for API requests.\r\n * Helps maintain consistent API communication when field naming differs.\r\n */ function mapFrontendToBackendFields(data) {\n    const fieldMapping = {\n        'lesson_ref': 'lessonRef',\n        'sessionId': 'session_id'\n    };\n    const result = {\n        ...data\n    };\n    // Apply field mappings\n    Object.entries(data).forEach((param)=>{\n        let [key, value] = param;\n        if (fieldMapping[key] && !result[fieldMapping[key]]) {\n            result[fieldMapping[key]] = value;\n        }\n    });\n    // Ensure critical fields are present\n    if (data.lesson_ref && !result.lessonRef) {\n        result.lessonRef = data.lesson_ref;\n    }\n    return result;\n}\n/**\r\n * Prepares a standardized request body for backend APIs, ensuring all required parameters are included.\r\n */ function prepareBackendRequest(request, body) {\n    // Extract context parameters\n    const context = extractContextFromRequest(request);\n    // Map frontend fields to backend fields\n    const mappedData = mapFrontendToBackendFields(body);\n    // Combine data with context parameters\n    return {\n        ...mappedData,\n        lessonRef: mappedData.lessonRef || body.lesson_ref,\n        student_id: context.studentId || mappedData.studentId || body.studentId,\n        country: context.country || mappedData.country || body.country,\n        curriculum: context.curriculum || mappedData.curriculum || body.curriculum,\n        grade: context.grade || mappedData.grade || body.grade,\n        level: context.level || mappedData.level || body.level,\n        subject: context.subject || mappedData.subject || body.subject\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils.ts\n"));

/***/ })

}]);