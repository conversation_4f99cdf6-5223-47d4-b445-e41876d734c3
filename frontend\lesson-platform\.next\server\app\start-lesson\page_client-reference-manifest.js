globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/start-lesson/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/app/client-providers.tsx":{"*":{"id":"(ssr)/./src/app/client-providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/providers/ThemeProvider.tsx":{"*":{"id":"(ssr)/./src/app/providers/ThemeProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ErrorBoundary.tsx":{"*":{"id":"(ssr)/./src/components/ErrorBoundary.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/loading.tsx":{"*":{"id":"(ssr)/./src/app/login/loading.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(ssr)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/start-lesson/page.tsx":{"*":{"id":"(ssr)/./src/app/start-lesson/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/classroom/page.tsx":{"*":{"id":"(ssr)/./src/app/classroom/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\src\\app\\client-providers.tsx":{"id":"(app-pages-browser)/./src/app/client-providers.tsx","name":"*","chunks":["firebase","static/chunks/firebase.js","common","static/chunks/common.js","app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["firebase","static/chunks/firebase.js","common","static/chunks/common.js","app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\src\\app\\providers\\ThemeProvider.tsx":{"id":"(app-pages-browser)/./src/app/providers/ThemeProvider.tsx","name":"*","chunks":["firebase","static/chunks/firebase.js","common","static/chunks/common.js","app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\src\\components\\ErrorBoundary.tsx":{"id":"(app-pages-browser)/./src/components/ErrorBoundary.tsx","name":"*","chunks":["firebase","static/chunks/firebase.js","common","static/chunks/common.js","app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\src\\app\\login\\loading.tsx":{"id":"(app-pages-browser)/./src/app/login/loading.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\src\\app\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\src\\app\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\src\\app\\start-lesson\\page.tsx":{"id":"(app-pages-browser)/./src/app/start-lesson/page.tsx","name":"*","chunks":["firebase","static/chunks/firebase.js","common","static/chunks/common.js","app/start-lesson/page","static/chunks/app/start-lesson/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\src\\app\\classroom\\page.tsx":{"id":"(app-pages-browser)/./src/app/classroom/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\src\\":[{"inlined":false,"path":"static/css/vendors.css"}],"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\src\\app\\layout":[{"inlined":false,"path":"static/css/vendors.css"},{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\src\\app\\start-lesson\\page":[{"inlined":false,"path":"static/css/vendors.css"}]},"rscModuleMapping":{"(app-pages-browser)/./src/app/client-providers.tsx":{"*":{"id":"(rsc)/./src/app/client-providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/providers/ThemeProvider.tsx":{"*":{"id":"(rsc)/./src/app/providers/ThemeProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ErrorBoundary.tsx":{"*":{"id":"(rsc)/./src/components/ErrorBoundary.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/loading.tsx":{"*":{"id":"(rsc)/./src/app/login/loading.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(rsc)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/start-lesson/page.tsx":{"*":{"id":"(rsc)/./src/app/start-lesson/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/classroom/page.tsx":{"*":{"id":"(rsc)/./src/app/classroom/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}