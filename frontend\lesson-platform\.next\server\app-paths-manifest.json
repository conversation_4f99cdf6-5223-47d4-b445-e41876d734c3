{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/auth/student-login/route": "app/api/auth/student-login/route.js", "/api/auth/get-user-role/route": "app/api/auth/get-user-role/route.js", "/api/timetable/route": "app/api/timetable/route.js", "/api/auth/set-student-claims/route": "app/api/auth/set-student-claims/route.js", "/api/lesson-content/route": "app/api/lesson-content/route.js", "/api/enhance-content/route": "app/api/enhance-content/route.js", "/login/page": "app/login/page.js", "/dashboard/page": "app/dashboard/page.js", "/start-lesson/page": "app/start-lesson/page.js", "/classroom/page": "app/classroom/page.js"}